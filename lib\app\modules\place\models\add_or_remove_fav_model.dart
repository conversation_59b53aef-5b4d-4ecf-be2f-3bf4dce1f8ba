class AddOrRemoveFavoriteModel {
  bool? status;
  String? message;

  AddOrRemoveFavoriteModel({
     this.status,
     this.message,
  });

  // Factory constructor to create the object from JSON
  factory AddOrRemoveFavoriteModel.fromJson(Map<String, dynamic> json) {
    return AddOrRemoveFavoriteModel(
      status: json['status'] ?? false, // Default to false if no value is found
      message: json['message'] ?? '',  // Default to empty string if no value is found
    );
  }

  // Method to convert the object back to JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
    };
  }
}
