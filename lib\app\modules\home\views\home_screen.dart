import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/home/<USER>/ads_controller.dart';
import 'package:ma3ak/app/modules/home/<USER>/offers_widget.dart';
import 'package:ma3ak/app/modules/home/<USER>/search_text_form_field.dart';
import 'package:ma3ak/app/modules/home/<USER>/slider.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/navbar/views/nav_bar_view.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/place/controller/greater_rate_place_controller.dart';
import 'package:ma3ak/widgets/shimmer/categories_shimmer.dart';
import 'package:ma3ak/widgets/shimmer/list_view_shimmer.dart';
import 'package:ma3ak/widgets/widgets/list_view_places_in_home.dart';
import 'package:ma3ak/widgets/widgets/main_app_bar.dart';
import 'package:ma3ak/widgets/shimmer/slider_shimmer.dart';

// Main home screen displaying ads, places, and offers
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  // Builds the home screen with various content sections
  @override
  Widget build(BuildContext context) {
    // Initialize controllers for different content sections
    final AdsController adsController = Get.put(AdsController());
    // final placeController = Get.put(PlaceController());
    final greaterRatePlaceController= Get.put(GreaterRatePlaceController());
    final OffersController offersController =
        Get.put<OffersController>(OffersController());

    // Main home screen layout
    return Column(
      children: [
        const MainAppBar(),
        Expanded(
          child: SafeArea(
            // Pull-to-refresh functionality for updating content
            child: RefreshIndicator(
              onRefresh: () async {
                adsController.fetchAds();
                greaterRatePlaceController.fetchGreaterRatePlace();
                // placeController.fetchPlaces();
                offersController.fetchOffers();
              },
              // Scrollable content area
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    // Search field that navigates to search screen
                    SearchTextFormField(
                      readOnly: true,
                      onTap: () {
                        Get.find<NavbarController>().selectedIndex.value = 3;
                        Get.to(() => const NavBarView());
                      },
                    ),
                    // Ads slider section with loading state
                    Obx(() {
                      return adsController.adData.value.data == null
                          ? const SliderSectionShimmer()
                          : adsController.adData.value.data!.isNotEmpty
                              ? const SliderWidget()
                              : const SizedBox();
                    }),
                    // Offers section with loading state
                    Obx(() => offersController.offerData.value.data == null
                        ? const Center(
                            child: Padding(
                            padding: EdgeInsets.only(top: 20),
                            child: OffersSectionShimmer(),
                          ))
                        : offersController.offerData.value.data!.isNotEmpty
                            ? OffersWidget()
                            : const SizedBox()),
                    const SizedBox(
                      height: 20,
                    ),
                    // High-rated places section with loading state
                    Obx(
                      () => greaterRatePlaceController.isLoading.value
                          ? const ListViewShimmer()
                          : greaterRatePlaceController.greaterRatePlaceData.value.data == null
                              ? const SizedBox()
                              : const ListViewPlacesInHome(
                                  isNeverScrollable: true,
                                ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
