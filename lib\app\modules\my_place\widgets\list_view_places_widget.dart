import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/favorite/controller/favorite_controller.dart';
import 'package:ma3ak/app/modules/my_place/controller/my_place_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/place/views/place_screen_owner.dart';
import 'package:ma3ak/app/modules/place/views/place_screen.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/widgets/card_widget.dart';

class ListViewMyPlacesWidget extends StatelessWidget {
  ListViewMyPlacesWidget({super.key, this.isNeverScrollable = false});
  final bool? isNeverScrollable;
  final MyPlaceController myPlaceController = Get.put(MyPlaceController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final placeCards = myPlaceController.myPlaceData.value.data;
      if (myPlaceController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }
      if (placeCards == null || placeCards.isEmpty) {
        return const Center(
          child: Text('لا يوجد أماكن لديك'),
        );
      }
      var owner = Get.find<ProfileController>().ownerProfileData.value.owner;
      return ListView.separated(
        shrinkWrap: true,
        physics: isNeverScrollable!
            ? const NeverScrollableScrollPhysics()
            : const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ),
        itemCount: placeCards.length,
        itemBuilder: (context, index) {
          final placeCard = placeCards[index];
          return InkWell(
            onTap: () async {
               Get.dialog(
  Center(
    child: Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
      color: AppColor.primaryColor
      ),
      height: 100.h, // Adjust height to your desired value
      width: 100.w,   // Adjust width to your desired value
      child: const Center(child: CircularProgressIndicator(color: Colors.white,),),
    ),
  ),
  barrierColor: Colors.transparent,
);
              final PlaceController placeController =
                  Get.find<PlaceController>();
              var res = await placeController.getPlaceInforamtion(
                  slugPlace: placeCard.slug);

              res.fold((error) {
                // Dismiss the loading dialog after data is fetched
                Get.back();
                debugPrint(error.toString());
              }, (dataModel) async {
                await Get.find<OffersController>()
                    .fetchOffersByPlace(slugPlace: placeCard.slug);
                placeController.placeInfromationOld.value = dataModel;
                // Dismiss the loading dialog after data is fetched
                Get.back();
                if (box.read('isOwner') == 1) {
                  Get.to(() => PlaceScreenOwner(
                        placeInfromation: placeController.placeInfromationOld,
                      ));
                } else {
                  Get.to(() => PlaceScreen(
                      placeInfromation: placeController.placeInfromationOld));
                }
              });
            },
            child: CardWidget(
              rating: placeCard.stars.toDouble(),
              title: placeCard.name,
              imgUrl: placeCard.coverImage,
              governor: placeCard.governor,
              directorate: placeCard.directorate,
              isFave: null,
              showIsEdit: placeCard.ownerId == owner?.id,
              onPressedEdit: () {
                if (placeCard.ownerId == owner?.id) {
                  Get.toNamed(Routes.updatePlace, arguments: placeCard);
                }
              },
              onPressed: () {
               if (box.read('token') != null && box.read('isOwner') != 1) {
                    Get.find<FavoriteController>()
                        .addOrRemoveFavorite(slugPlace: placeCard.slug);
                  } else {
                    Get.snackbar('الرسالة', 'يجب تسجيل الدخول اولا',
                        backgroundColor: Colors.green,
                        colorText: Colors.white,
                        icon: const Icon(Icons.warning, color: Colors.white));
                  }
              },
            ),
          );
        },
        separatorBuilder: (context, index) {
          return const Divider();
        },
      );
    });
  }
}
