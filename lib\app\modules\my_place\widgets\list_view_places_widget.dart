import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/favorite/controller/favorite_controller.dart';
import 'package:ma3ak/app/modules/my_place/controller/my_place_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/place/views/place_screen_owner.dart';
import 'package:ma3ak/app/modules/place/views/place_screen.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/widgets/card_widget.dart';

/// Widget displaying a list of places owned by the current user
/// Shows place cards with edit functionality and navigation options
class ListViewMyPlacesWidget extends StatelessWidget {
  ListViewMyPlacesWidget({super.key, this.isNeverScrollable = false});

  // Controls whether the list view is scrollable
  final bool? isNeverScrollable;

  // Controller for managing user's places data
  final MyPlaceController myPlaceController = Get.put(MyPlaceController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Get places data from controller
      final placeCards = myPlaceController.myPlaceData.value.data;

      // Show loading indicator while fetching data
      if (myPlaceController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      // Show empty state message when no places exist
      if (placeCards == null || placeCards.isEmpty) {
        return const Center(
          child: Text('لا يوجد أماكن لديك'),
        );
      }

      // Get current owner information for permission checks
      var owner = Get.find<ProfileController>().ownerProfileData.value.owner;
      // Build scrollable list of place cards
      return ListView.separated(
        shrinkWrap: true,
        physics: isNeverScrollable!
            ? const NeverScrollableScrollPhysics()
            : const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: placeCards.length,
        itemBuilder: (context, index) {
          final placeCard = placeCards[index];
          return InkWell(
            onTap: () async {
              // Show loading dialog while fetching place details
              Get.dialog(
                Center(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: AppColor.primaryColor
                    ),
                    height: 100.h,
                    width: 100.w,
                    child: const Center(
                      child: CircularProgressIndicator(color: Colors.white)
                    ),
                  ),
                ),
                barrierColor: Colors.transparent,
              );

              // Fetch detailed place information
              final PlaceController placeController = Get.find<PlaceController>();
              var res = await placeController.getPlaceInforamtion(
                  slugPlace: placeCard.slug);

              res.fold((error) {
                // Handle error and dismiss loading dialog
                Get.back();
                debugPrint(error.toString());
              }, (dataModel) async {
                // Fetch place offers and navigate to appropriate screen
                await Get.find<OffersController>()
                    .fetchOffersByPlace(slugPlace: placeCard.slug);
                placeController.placeInfromationOld.value = dataModel;
                Get.back(); // Dismiss loading dialog

                // Navigate to owner or regular place screen based on user type
                if (box.read('isOwner') == 1) {
                  Get.to(() => PlaceScreenOwner(
                        placeInfromation: placeController.placeInfromationOld,
                      ));
                } else {
                  Get.to(() => PlaceScreen(
                      placeInfromation: placeController.placeInfromationOld));
                }
              });
            },
            // Place card widget with interactive features
            child: CardWidget(
              rating: placeCard.stars.toDouble(),
              title: placeCard.name,
              imgUrl: placeCard.coverImage,
              governor: placeCard.governor,
              directorate: placeCard.directorate,
              isFave: null,
              // Show edit button only for place owners
              showIsEdit: placeCard.ownerId == owner?.id,
              onPressedEdit: () {
                // Navigate to update place screen if user owns the place
                if (placeCard.ownerId == owner?.id) {
                  Get.toNamed(Routes.updatePlace, arguments: placeCard);
                }
              },
              onPressed: () {
                // Handle favorite toggle with authentication check
                if (box.read('token') != null && box.read('isOwner') != 1) {
                  Get.find<FavoriteController>()
                      .addOrRemoveFavorite(slugPlace: placeCard.slug);
                } else {
                  Get.snackbar('الرسالة', 'يجب تسجيل الدخول اولا',
                      backgroundColor: Colors.green,
                      colorText: Colors.white,
                      icon: const Icon(Icons.warning, color: Colors.white));
                  }
              },
            ),
          );
        },
        // Add divider between list items
        separatorBuilder: (context, index) {
          return const Divider();
        },
      );
    });
  }
}
