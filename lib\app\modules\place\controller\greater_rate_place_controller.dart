import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/place_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class GreaterRatePlaceController extends GetxController{
  var isLoading = false.obs;  
  var greaterRatePlaceData = PlaceModel().obs;  



  Future<void> fetchGreaterRatePlace() async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .fetchGreaterRatePlace(); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      greaterRatePlaceData.value = dataModel;
      debugPrint(greaterRatePlaceData.value.toString());
    });
  }


  @override
  void onInit() {
    fetchGreaterRatePlace();
    super.onInit();
  }
}