import 'package:dartz/dartz.dart';
import 'package:ma3ak/app/modules/auth/models/owner_model.dart';
import 'package:ma3ak/app/modules/auth/models/user_model.dart';
import 'package:ma3ak/app/modules/auth/models/user_registeration_model.dart';
import 'package:ma3ak/app/modules/dashboard/models/dashboard_model.dart';
import 'package:ma3ak/app/modules/home/<USER>/ads_model.dart';
import 'package:ma3ak/app/modules/my_place/models/create_offer_model.dart';
import 'package:ma3ak/app/modules/offers/models/like_model.dart';
import 'package:ma3ak/app/modules/offers/models/offer_model.dart';
import 'package:ma3ak/app/modules/offers/models/place_information_old_model.dart';
import 'package:ma3ak/app/modules/place/models/add_or_remove_fav_model.dart';
import 'package:ma3ak/app/modules/place/models/comments_place_model.dart';
import 'package:ma3ak/app/modules/place/models/ctegoreis_model.dart';
import 'package:ma3ak/app/modules/place/models/place_model.dart';
import 'package:ma3ak/app/modules/place/models/rating_model.dart';
import 'package:ma3ak/app/modules/place/models/sections_model.dart';
import 'package:ma3ak/app/modules/place/models/semd_comment_model.dart';
import 'package:ma3ak/app/modules/profile/models/logout_account_model.dart';
import 'package:ma3ak/app/modules/profile/models/owner_profile_model.dart';
import 'package:ma3ak/app/modules/profile/models/user_profile_model.dart';
import 'package:ma3ak/app/modules/update_place/models/update_place_model.dart';
import 'package:ma3ak/data/api/api_server.dart';
import 'package:ma3ak/data/api/remote_services.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/main.dart';

// Implementation of remote services for API calls and data operations
class RemoteServicesImpl extends RemoteServices {
  // User authentication token from local storage
  var token = box.read('token');
  
  // Fetches user profile data from API
  Future<Either<String, UserProfileModel>> getProfileUser() async {
    // Set up authentication headers
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    
    // Make API call to get user profile
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.getPofileUser,
      headers: headers,
    );

    // Handle API response
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(
        UserProfileModel.fromJson(data),
      );
    } else if (response.statusCode == 401) {
      box.remove('token');
      box.remove('isOwner');
      return const Left("المستخدم محظور");
    } else {
      return const Left("Something went wrong get user profile.");
    }
  }

  // Updates user profile information via API
  Future<Map<String, dynamic>> updateUserProfile({
    dynamic body,
  }) async {
    var token = await box.read('token');
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
    };

    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.updateUserProfile,
      body: body,
      headers: headers,
      isGet: false,
    );

    if (response.statusCode == 200) {
      return response.body as Map<String, dynamic>; // Return the body as a map
    } else {
      throw Exception('Failed to update user profile');
    }
  }

  Future<Map<String, dynamic>> updateOwnerProfile({
    dynamic body,
  }) async {
    var token = box.read('token');
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      // 'Content-Type': 'application/json',
    };

    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.updateOwnerProfile,
      body: body,
      headers: headers,
      isGet: false,
    );

    if (response.statusCode == 200) {
      return response.body as Map<String, dynamic>; // Return the body as a map
    } else {
      throw Exception('Failed to update owner profile');
    }
  }

  Future<Either<String, OwnerProfileModel>> getProfileOwner(
      String token) async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.getPofileOwner,
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(OwnerProfileModel.fromJson(data));
    } else if (response.statusCode == 401) {
      box.remove('token');
      box.remove('isOwner');
      return const Left("المستخدم محظور");
    } else {
      return const Left("Something went wrong getProfileOwner.");
    }
  }

  Future<Either<String, AdModel>> fetchAds() async {
    final headers = {
      'Accept': '*/*',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer()
        .getOrPostMethodData(headers: headers, isGet: true, url: ApiList.ads);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(AdModel.fromJson(data));
    } else {
      return const Left("Something went wrong ads.");
    }
  }

  Future<Either<String, OfferModel>> fetchOffers() async {
    final response = await ApiServer()
        .getOrPostMethodData(isGet: true, url: ApiList.allOffers);
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(OfferModel.fromJson(data));
    } else {
      return const Left("Something went wrong in fetching offers.");
    }
  }

  Future<Either<String, OfferModel>> fetchMyOffersOffers() async {
    final header = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
        isGet: true, url: ApiList.myOffers, headers: header);
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(OfferModel.fromJson(data));
    } else {
      return const Left("Something went wrong in fetching my offers.");
    }
  }

  Future<Either<String, CreateOfferModel>> createOffer(dynamic body,
      {required String slugPlace}) async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
    };

    final response = await ApiServer().getOrPostMethodData(
      isGet: false,
      url: ApiList.createOffer + slugPlace,
      body: body,
      headers: headers,
      // contentType: "multipart/form-data",
    );
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(CreateOfferModel.fromJson(data));
    } else {
      return const Left("Something went wrong in createOffer.");
    }
  }

  Future<Either<String, OfferModel>> fetchOffersByPlace(
      {required String slugPlace}) async {
    final response = await ApiServer().getOrPostMethodData(
        isGet: true, url: ApiList.allOffersInPlace + slugPlace);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(OfferModel.fromJson(data));
    } else {
      return const Left("Something went wrong.in createOffer");
    }
  }

  Future<Either<String, OfferModel>> fetchOffersByType(
      {required String type}) async {
    final response = await ApiServer()
        .getOrPostMethodData(isGet: true, url: ApiList.getOffersByType + type);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(OfferModel.fromJson(data));
    } else {
      return const Left("Something went wrong.in createOffer");
    }
  }

  Future<Either<String, dynamic>> deleteOffer(
      {required String slugOffer}) async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',

      /// returns an [Either] with either the deleted [OfferModel] on success or a
      /// [String] error message on failure
    };
    final response = await ApiServer().getOrPostMethodData(
        headers: headers, isGet: true, url: ApiList.deleteOffer + slugOffer);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(data);
    } else {
      return const Left("Something went wrong.in deleteOffer");
    }
  }

  Future<Either<String, dynamic>> updateOffer(
      {required String slugOffer, required dynamic body}) async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      // 'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
        body: body,
        headers: headers,
        isGet: false,
        url: ApiList.updateOffer + slugOffer);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(data);
    } else {
      return const Left("Something went wrong.in updateOffer");
    }
  }

  Future<Either<String, SendCommentModel>> sendComment(
      {required String slugPlace, dynamic body}) async {
    var token = box.read('token');
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      body: body,
      isGet: false,
      headers: headers,
      url: ApiList.sendComment + slugPlace,
    );

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(SendCommentModel.fromJson(data));
    } else {
      return const Left("Something went wrong.SendCommentModel");
    }
  }

  Future<Either<String, SendCommentModel>> sendReplyComment(
      {required int commentId, dynamic body}) async {
    var token = box.read('token');
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      body: body,
      isGet: false,
      headers: headers,
      url: ApiList.replyComment + commentId.toString(),
    );

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(SendCommentModel.fromJson(data));
    } else {
      return const Left("Something went wrong.SendCommentModel");
    }
  }

  Future<Either<String, PlaceInfromationOldModel>> getPlaceByOffer(
      {required String slugOffer}) async {
    final response = await ApiServer().getOrPostMethodData(

        /// with the specified [slugOffer]. On a successful request, it returns a
        /// [PlaceDetailsModel] wrapped in a [Right]. If the request fails, it
        isGet: true,
        url: ApiList.getPlaceByOffer + slugOffer);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(PlaceInfromationOldModel.fromJson(data));
    } else {
      return const Left("Something went wrong.PlaceDetailsModel");
    }
  }

  Future<Either<String, PlaceInfromationOldModel>> getPlaceInformation(
      {required slugPlace}) async {
    final response = await ApiServer()
        .getOrPostMethodData(isGet: true, url: ApiList.getPlaces + slugPlace);


    if (response.statusCode == 200) {
      final data = response.body;
      return Right(PlaceInfromationOldModel.fromJson(data));
    } else {
      return const Left("Something went wrong getPlaceInformation.");
    }
  }

  Future<Either<String, RatingModel>> userRating(
      {required String slugPlace, required dynamic body}) async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
        body: body,
        headers: headers,
        isGet: false,
        url: ApiList.userRating + slugPlace);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(RatingModel.fromJson(data));
    } else {
      return const Left("Something went wrong in userRating.");
    }
      }

  Future<Either<String, PlaceModel>> fetchPlaces() async {
    final response = await ApiServer()
        .getOrPostMethodData(isGet: true, url: ApiList.getPlaces);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(PlaceModel.fromJson(data));
    } else {
      return const Left("Something went wrong places.");
    }
  }

  Future<Either<String, PlaceModel>> fetchMyPlaces() async {
    final header = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
        headers: header, isGet: true, url: ApiList.myPlaces);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(PlaceModel.fromJson(data));
    } else {
      return const Left("Something went wrong my places.");
    }
  }

  Future<Either<String, SectionsModel>> fetchSections() async {
    final response = await ApiServer()
        .getOrPostMethodData(isGet: true, url: ApiList.getSections);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(SectionsModel.fromJson(data));
    } else {
      return const Left("Something went wrong SectionsModel.");
    }
  }

  Future<Either<String, CategoriesModel>> fetchCategoriesAndPlaceBySection(
      {required String slugScetion}) async {
    final response = await ApiServer().getOrPostMethodData(
        isGet: true, url: ApiList.getCategories + slugScetion);

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(CategoriesModel.fromJson(data));
    } else {
      return const Left("Something went wrong SectionsModel.");
    }
  }

  Future<Either<String, CommentsPlaceModel>> fetchCommentsPlace(
      {required String slugPlace}) async {
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.getCommentsForPlace + slugPlace,
      isGet: true,
    );

    if (response.statusCode == 200) {
      final data = response.body;
      return Right(CommentsPlaceModel.fromJson(data));
    } else {
      return const Left("Something went wrong.fetchCommentsPlace");
    }
  }

  Future<Either<String, UserRegistrationModel>> registerUser(
      String phoneNumber, String name, String email, String password) async {
    final body = {
      "name": name,
      "email": email,
      "phone_number": phoneNumber,
      "password": password,
      "confirm_password": password,
    };
    final headers = {
      'Accept': '*/*',
      // 'Authorization': 'Bearer $token',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
        isGet: false, url: ApiList.registerUser, body: body, headers: headers);

    if (response.statusCode == 200) {
      final data = response.body;
      box.write('token', data['token']);
      if (data['isOwner'] == null || data['isOwner'] == 0) {
        box.write('isOwner', 0);
        update();
      } else {
        box.write('isOwner', data['isOwner']);
        update();
      }
      return Right(UserRegistrationModel.fromJson(data));
    } else {
      return Left("Registration failed: ${response.body['message']}");
    }
  }

  Future<Either<String, dynamic>> loginUser(
      String email, String password) async {
    final headers = {
      'Accept': '*/*',
      // 'Authorization': 'Bearer $token',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final body = {
      "email": email,
      "password": password,
    };

    final response = await ApiServer().getOrPostMethodData(
        isGet: false, url: ApiList.loginUser, body: body, headers: headers);

    // print('status is' + response.bod['status']);
    if (response.statusCode == 200) {
      final data = response.body;
      box.write('token', data['token']);
      box.write('isOwner', data['isOwner']);

      if (data['isOwner'] == 1) {
        return Right(OwnerModel.fromJson(data));
      }

      return Right(UserModel.fromJson(data));
    } else if (response.statusCode == 401) {
      box.remove('token');
      box.remove('isOwner');
      return const Left("المستخدم محظور");
    } else if (response.statusCode == 403) {
      box.remove('token');
      box.remove('isOwner');
      return const Left("اسم المستخدم او كلمة المرور غير صحيحة");
    } else {
      return const Left("Login failed: ");
    }
  }

  Future<Either<String, dynamic>> getFavoriteUser() async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer ${box.read('token')}',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.getFavorite,
      headers: headers,
    );
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(PlaceModel.fromJson(data));
    } else {
      return const Left("Something went wrong.");
    }
  }

  Future<Either<String, LikeModel>> likeOffer(
      {required String slugOffer}) async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer ${box.read('token')}',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.likeOffer + slugOffer,
      headers: headers,
    );
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(LikeModel.fromJson(data));
    } else {
      return const Left("Something went wrong in LikeModel");
    }
  }

  Future<Either<String, LikeModel>> disLikeOffer(
      {required String slugOffer}) async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer ${box.read('token')}',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.disLikeOffer + slugOffer,
      headers: headers,
    );
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(LikeModel.fromJson(data));
    } else {
      return const Left("Something went wrong in disLikeModel");
    }
  }

  Future<Either<String, AddOrRemoveFavoriteModel>> addOrRemoveFavorite(
      {required String slugPlace}) async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer ${box.read('token')}',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.addOrRemoveFavorite + slugPlace,
      headers: headers,
    );
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(AddOrRemoveFavoriteModel.fromJson(data));
    } else {
      return const Left("Something went wrong in AddOrRemoveFavoriteModel.");
    }
  }

  @override
  Future<Either<String, LogoutAccountModel>> logoutUser() async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer ${box.read('token')}',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.logoutUser,
      headers: headers,
    );
    if (response.statusCode == 200) {
      box.remove('token');
      box.remove('isOwner');
      final data = response.body;
      return Right(LogoutAccountModel.fromJson(data));
    } else {
      return const Left("Something went wrong in LogoutAccountModel.");
    }
  }

  @override
  Future<Either<String, LogoutAccountModel>> logoutOwner() async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer ${box.read('token')}',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.logoutOwner,
      headers: headers,
    );
    if (response.statusCode == 200) {
      box.remove('token');
      box.remove('isOwner');
      final data = response.body;
      return Right(LogoutAccountModel.fromJson(data));
    } else {
      return const Left("Something went wrong in LogoutAccountModel.");
    }
  }

  @override
  Future<Either<String, DashboardModel>> fetchDashboard() async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer ${box.read('token')}',
      // 'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.dashboardOwner,
      headers: headers,
      isGet: true,
    );
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(DashboardModel.fromJson(data));
    } else {
      return const Left("Something went wrong in DashboardModel.");
    }
  }

  @override
  Future<Either<String, UpdatePlaceModel>> updatePlace(
      {required dynamic body, required String slugPlace}) async {
    final headers = {
      'Accept': '*/*',
      'Authorization': 'Bearer $token',
    };
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.updatePlaceOwner + slugPlace,
      headers: headers,
      body: body,
      isGet: false,
    );
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(UpdatePlaceModel.fromJson(data));
    } else {
      return const Left("Something went wrong in UpdatePlaceModel.");
    }
  }
  
  @override
  Future<Either<String, PlaceModel>> fetchGreaterRatePlace() async{
    final response = await ApiServer().getOrPostMethodData(
      url: ApiList.greaterRatePlace,
    );
    if (response.statusCode == 200) {
      final data = response.body;
      return Right(PlaceModel.fromJson(data));
    } else {
      return const Left("Something went wrong in fetchGreaterRatePlace.");
    }
  }
}
