import 'dart:convert';

/// Helper functions for JSON serialization/deserialization
OfferModel offerModelFromJson(String str) =>
    OfferModel.fromJson(json.decode(str));

String offerModelToJson(OfferModel data) => json.encode(data.toJson());

/// Model representing API response containing offers data
class OfferModel {
  // Response status indicating success/failure
  final bool? status;
  
  // Response message with additional information
  final String? message;
  
  // List of offer data items
  final List<OfferDatum>? data;

  OfferModel({
    this.status,
    this.message,
    this.data,
  });

  // Creates OfferModel from JSON response
  factory OfferModel.fromJson(Map<String, dynamic> json) => OfferModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? null
            : List<OfferDatum>.from(json["data"]
                .map((x) => OfferDatum.fromJson(x))), // Parse as a list
      );

  // Converts OfferModel to JSON format
  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null
            ? null
            : List<dynamic>.from(
                data!.map((x) => x.toJson())), // Convert to list
      };
}

/// Individual offer data model containing offer details
class OfferDatum {
  // Unique offer identifier
  final int id;
  
  // Offer title/name
  final String title;
  
  // Offer description
  final String des;
  
  // Number of likes as string
  final String likes;
  
  // Number of dislikes as string
  final String dislikes;
  
  // Offer type/category
  final String type;
  
  // Offer image URL
  final String img;
  
  // URL slug for the offer
  final String slug;
  
  // ID of the business owner
  final int ownerId;
  
  // ID of the associated place
  final int placeId;
  
  // Last update timestamp
  final DateTime updatedAt;
  
  // Creation timestamp
  final DateTime createdAt;

  // Constructor requiring all offer data fields
  OfferDatum({
    required this.id,
    required this.title,
    required this.des,
    required this.likes,
    required this.dislikes,
    required this.type,
    required this.img,
    required this.slug,
    required this.ownerId,
    required this.placeId,
    required this.updatedAt,
    required this.createdAt,
  });

  // Creates OfferDatum from JSON response
  factory OfferDatum.fromJson(Map<String, dynamic> json) => OfferDatum(
        id: json["id"],
        title: json["title"],
        des: json["des"],
        likes: json["likes"],
        dislikes: json["dislikes"],
        type: json["type"],
        img: json["img"],
        slug: json["slug"],
        ownerId: json["owner_id"],
        placeId: json["place_id"],
        updatedAt: DateTime.parse(json["updated_at"]),
        createdAt: DateTime.parse(json["created_at"]),
      );

  // Converts OfferDatum to JSON format
  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "des": des,
        "likes": likes,
        "dislikes": dislikes,
        "type": type,
        "img": img,
        "slug": slug,
        "owner_id": ownerId,
        "place_id": placeId,
        "updated_at": updatedAt.toIso8601String(),
        "created_at": createdAt.toIso8601String(),
      };
}
