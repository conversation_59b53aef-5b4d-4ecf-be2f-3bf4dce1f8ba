import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/app/modules/update_profile/controller/update_profle_controller.dart';
import 'package:ma3ak/app/modules/update_profile/widgets/text_form_field_update.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/reactive_button.dart';

class UpdateProfileScreen extends StatelessWidget {
  UpdateProfileScreen({super.key});
  final UpdateProfleController updateProfleController =
      Get.find<UpdateProfleController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(),
        body: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  Obx(
                    () => CircleAvatar(
                      radius: 50,
                      backgroundImage: updateProfleController
                              .selectedImagePath.value.isNotEmpty
                          ? FileImage(File(
                              updateProfleController.selectedImagePath.value))
                          : null,
                      child: Container(
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            image: updateProfleController
                                    .selectedImagePath.value.isNotEmpty
                                ? DecorationImage(
                                    image: FileImage(File(updateProfleController
                                        .selectedImagePath.value)))
                                : null),
                      ),
                    ),
                  ),
                  Positioned(
                      bottom: -20,
                      left: 0,
                      right: 0,
                      child: IconButton(
                          onPressed: () {
                            updateProfleController.pickOneImage();
                          },
                          icon: const Icon(
                            Icons.edit,
                            color: Colors.blue,
                          )))
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              TextFormFieldUpdate(
                controller: updateProfleController.nameController,
                labelText: 'اسم المستخدم',
                hintText: 'ادخل اسم المستخدم',
              ),
              TextFormFieldUpdate(
                controller: updateProfleController.emailController,
                labelText: 'البريد الالكتروني',
                hintText: 'ادخل البريد الالكتروني',
              ),
              TextFormFieldUpdate(
                controller: updateProfleController.phoneNumberController,
                labelText: 'رقم الهاتف',
                hintText: 'ادخل رقم الهاتف',
              ),
              TextFormFieldUpdate(
                controller: updateProfleController.passwordController,
                labelText: 'كلمة المرور',
                hintText: 'ادخل كلمة المرور',
              ),
              TextFormFieldUpdate(
                controller: updateProfleController.confirmPasswordController,
                obscureText: true,
                labelText: 'تأكيد كلمة المرور',
                hintText: 'ادخل تأكيد كلمة المرور',
              ),
              const SizedBox(
                height: 20,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ReactiveButton(
                    onPressed: () async {
                      final reactiveButtonController =
                          Get.find<ReactiveButtonController>();
                      if (box.read('isOwner') == 0) {
                        reactiveButtonController.isLoading.value = true;
                        await updateProfleController.updateUserProfile();
                        reactiveButtonController.isLoading.value = false;
                      } else if (box.read('isOwner') == 1) {
                        reactiveButtonController.isLoading.value = true;
                        await updateProfleController.updateOwnerProfile();
                        reactiveButtonController.isLoading.value = false;
                      }
                    },
                    wdth: double.infinity,
                    text: 'تعديل الملف الشخصي'),
              )
            ],
          ),
        ));
  }
}
