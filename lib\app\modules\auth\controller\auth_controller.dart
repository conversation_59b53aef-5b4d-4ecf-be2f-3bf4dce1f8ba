import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/models/owner_model.dart';
import 'package:ma3ak/app/modules/auth/models/user_model.dart';
import 'package:ma3ak/app/modules/auth/models/user_registeration_model.dart';
import 'package:ma3ak/app/modules/navbar/views/nav_bar_view.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';
import 'package:ma3ak/main.dart';

// Controller for handling user authentication (login/register/logout)
class AuthController extends GetxController {
  // Form controllers for authentication input fields
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController emailOwnerController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController passwordOwnerController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  
  // Reactive state variables
  final isLoading = false.obs;
  final userData = UserModel().obs;
  final ownerData = OwnerModel().obs;
  final Rx<UserRegistrationModel> userRegisterationModel =
      UserRegistrationModel().obs;

  // Saves user authentication token to local storage
  void saveUserData(String? token) {
    box.write('token', token);
  }

  // Signs out user by clearing stored data and navigating to main screen
  Future<void>? signOut() {
    box.remove('token');
    box.remove('isOwner');
    // userData.value = UserModel(); // Reset user data
    Get.offAll(() =>  const NavBarView()); // Navigate to login screen
    return null;
  }

  // Registers a new user with provided credentials
  Future<Either?> registerUser(
      String phoneNumber, String name, String email, String password) async {
    isLoading(true);
    
    // Make API call to register user
    final response = await RemoteServicesImpl()
        .registerUser(phoneNumber, name, email, password);
    isLoading(false);
    
    // Handle registration response
    response.fold(
      (l) => Get.snackbar("Error", l),
      (userRegisterModel) {
        userRegisterationModel.value = userRegisterModel;
        Get.offAndToNamed(Routes.navBarView);
        // Get.offAll(() => const NavBarView());
        Get.snackbar(
          "Success",
          "تم تسجيل الدخول بنجاح",
          // userRegisterModel.message.toString(),
        );
      },
    );
    return null;
  }

  // Authenticates user with email and password
  Future<void> loginUser(String email, String password) async {
    isLoading(true);
    
    // Make API call to login user
    final response = await RemoteServicesImpl().loginUser(email, password);
    isLoading(false);
    Get.find<ReactiveButtonController>().isLoading.value = false;
    
    // Handle login response
    response.fold((l) => Get.snackbar("Error", l), (userOrOwnerModel) {
      // Check if user is owner or regular user
      if (box.read('isOwner') == 1) {
        ownerData.value = userOrOwnerModel;
      } else {
        userData.value = userOrOwnerModel;
        Get.offAndToNamed(Routes.navBarView);
        // final navController = Get.put(NavbarController());
        // Get.offAll(() => const NavBarView());
        // navController.selectPage(0);
      }
      // Get.off(() => const NavBarView());
      Get.offAndToNamed(Routes.navBarView);
      Get.snackbar(
        "Success",
        "تم تسجيل الدخول بنجاح",
        // userOrOwnerModel.message.toString(),
      );
    });
    Get.find<ReactiveButtonController>().isLoading.value = false;
  }
}
