import 'dart:convert';

// JSON parsing utility functions for CommentsPlaceModel
CommentsPlaceModel commentsPlaceModelFromJson(String str) =>
    CommentsPlaceModel.fromJson(json.decode(str));

String commentsPlaceModelToJson(CommentsPlaceModel data) =>
    json.encode(data.toJson());

/// Model representing API response for place comments data
class CommentsPlaceModel {
  // Response status indicating success/failure
  final bool? status;

  // Response message with additional information
  final String? message;

  // Place data containing comments and place details
  final CommentsPlaceDatum? data;

  CommentsPlaceModel({
    this.status,
    this.message,
    this.data,
  });

  // Creates CommentsPlaceModel from JSON response
  factory CommentsPlaceModel.fromJson(Map<String, dynamic> json) =>
      CommentsPlaceModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : CommentsPlaceDatum.fromJson(json["data"]),
      );

  // Converts CommentsPlaceModel to JSON format
  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

/// Model representing place data with associated comments
class CommentsPlaceDatum {
  // Unique place identifier
  final int id;

  // Place name/title
  final String name;

  // Governor/state location
  final String governor;

  // Directorate/district location
  final String directorate;

  // Detailed address information
  final String addressDetails;

  // Place description
  final String des;

  // WhatsApp contact number
  final int whatsappNumber;

  // Cover image URL
  final String coverImage;

  // URL slug for the place
  final String slug;

  // Star rating (1-5)
  final int stars;

  // Place status (active/inactive)
  final String status;

  // Parent place ID (optional for sub-places)
  final int? parentId;

  // Category ID
  final int catId;

  // Owner user ID
  final int ownerId;

  // Place owner information
  final Owner owner;

  // List of comments for this place
  final List<Comment> comments;

  CommentsPlaceDatum({
    required this.id,
    required this.name,
    required this.governor,
    required this.directorate,
    required this.addressDetails,
    required this.des,
    required this.whatsappNumber,
    required this.coverImage,
    required this.slug,
    required this.stars,
    required this.status,
    this.parentId,
    required this.catId,
    required this.ownerId,
    required this.owner,
    required this.comments,
  });

  // Creates CommentsPlaceDatum from JSON data
  factory CommentsPlaceDatum.fromJson(Map<String, dynamic> json) =>
      CommentsPlaceDatum(
        id: json["id"],
        name: json["name"],
        governor: json["governor"],
        directorate: json["directorate"],
        addressDetails: json["address_details"],
        des: json["des"],
        whatsappNumber: json["whatsapp_number"],
        coverImage: json["cover_image"],
        slug: json["slug"],
        stars: json["stars"],
        status: json["status"],
        parentId: json["parent_id"],
        catId: json["cat_id"],
        ownerId: json["owner_id"],
        owner: Owner.fromJson(json["owner"]),
        comments: json["comments"] == null
            ? []
            : List<Comment>.from(
                json["comments"].map((x) => Comment.fromJson(x))),
      );

  // Converts CommentsPlaceDatum to JSON format
  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "governor": governor,
        "directorate": directorate,
        "address_details": addressDetails,
        "des": des,
        "whatsapp_number": whatsappNumber,
        "cover_image": coverImage,
        "slug": slug,
        "stars": stars,
        "status": status,
        "parent_id": parentId,
        "cat_id": catId,
        "owner_id": ownerId,
        "owner": owner.toJson(),
        "comments": List<dynamic>.from(comments.map((x) => x.toJson())),
      };
}

/// Model representing place owner information
class Owner {
  // Unique owner identifier
  final int id;

  // Owner name
  final String name;

  Owner({
    required this.id,
    required this.name,
  });

  // Creates Owner from JSON data
  factory Owner.fromJson(Map<String, dynamic> json) => Owner(
        id: json["id"],
        name: json["name"],
      );

  // Converts Owner to JSON format
  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

/// Model representing a comment on a place
class Comment {
  // Unique comment identifier
  final int id;

  // Comment text content
  final String comment;

  // ID of the place this comment belongs to
  final int placeId;

  // ID of the user who made the comment
  final int userId;

  // Parent comment ID (for nested replies)
  final int? parentId;

  // Comment creation timestamp
  final String createdAt;

  // Comment last update timestamp
  final String updatedAt;

  // User information who made the comment
  final User user;

  // Optional reply to this comment
  final Reply? reply;

  Comment({
    required this.id,
    required this.comment,
    required this.placeId,
    required this.userId,
    this.parentId,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
    this.reply,
  });

  // Creates Comment from JSON data
  factory Comment.fromJson(Map<String, dynamic> json) => Comment(
        id: json["id"],
        comment: json["comment"],
        placeId: json["place_id"],
        userId: json["user_id"],
        parentId: json["parent_id"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        user: User.fromJson(json["user"]),
        reply: json["replies"] != null ? Reply.fromJson(json["replies"]) : null,
      );

  // Converts Comment to JSON format
  Map<String, dynamic> toJson() => {
        "id": id,
        "comment": comment,
        "place_id": placeId,
        "user_id": userId,
        "parent_id": parentId,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "user": user.toJson(),
        "replies": reply?.toJson(),
      };
}

/// Model representing user information for comments
class User {
  // Unique user identifier
  final int id;

  // User display name
  final String name;

  // User profile image URL
  final String img;

  User({
    required this.id,
    required this.name,
    required this.img,
  });

  // Creates User from JSON data
  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"],
        name: json["name"],
        img: json["img"],
      );

  // Converts User to JSON format
  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "img": img,
      };
}

/// Model representing a reply to a comment
class Reply {
  // Unique reply identifier
  final int id;

  // Reply text content
  final String comment;

  // ID of the place this reply belongs to
  final int placeId;

  // ID of the user who made the reply
  final int userId;

  // ID of the parent comment being replied to
  final int parentId;

  // Reply creation timestamp
  final String createdAt;

  // Reply last update timestamp
  final String updatedAt;

  // User information who made the reply
  final User user;

  Reply({
    required this.id,
    required this.comment,
    required this.placeId,
    required this.userId,
    required this.parentId,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
  });

  // Creates Reply from JSON data
  factory Reply.fromJson(Map<String, dynamic> json) => Reply(
        id: json["id"],
        comment: json["comment"],
        placeId: json["place_id"],
        userId: json["user_id"],
        parentId: json["parent_id"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        user: User.fromJson(json["user"]),
      );

  // Converts Reply to JSON format
  Map<String, dynamic> toJson() => {
        "id": id,
        "comment": comment,
        "place_id": placeId,
        "user_id": userId,
        "parent_id": parentId,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "user": user.toJson(),
      };
}
