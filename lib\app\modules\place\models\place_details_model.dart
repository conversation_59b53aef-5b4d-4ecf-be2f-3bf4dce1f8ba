import 'dart:convert';

// To parse this JSON data, do
//
//     final placeDetailsModel = placeDetailsModelFromJson(jsonString);

PlaceDetailsModel placeDetailsModelFromJson(String str) => PlaceDetailsModel.fromJson(json.decode(str));

String placeDetailsModelToJson(PlaceDetailsModel data) => json.encode(data.toJson());

class PlaceDetailsModel {
  final bool? status;
  final String? message;
  final PlaceDatum? data;

  PlaceDetailsModel({
    this.status,
    this.message,
    this.data,
  });

  factory PlaceDetailsModel.fromJson(Map<String, dynamic> json) => PlaceDetailsModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : PlaceDatum.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class PlaceDatum {
  final int id;
  final String name;
  final String governor;
  final String directorate;
  final String addressDetails;
  final String des;
  final int whatsappNumber;
  final String coverImage;
  final String slug;
  final int stars;
  final String status;
  final int? parentId;
  final int catId;
  final int ownerId;
  final Owner owner;
  final List<Comment> comments;

  PlaceDatum({
    required this.id,
    required this.name,
    required this.governor,
    required this.directorate,
    required this.addressDetails,
    required this.des,
    required this.whatsappNumber,
    required this.coverImage,
    required this.slug,
    required this.stars,
    required this.status,
    this.parentId,
    required this.catId,
    required this.ownerId,
    required this.owner,
    required this.comments,
  });

  factory PlaceDatum.fromJson(Map<String, dynamic> json) => PlaceDatum(
        id: json["id"],
        name: json["name"],
        governor: json["governor"],
        directorate: json["directorate"],
        addressDetails: json["address_details"],
        des: json["des"],
        whatsappNumber: json["whatsapp_number"],
        coverImage: json["cover_image"],
        slug: json["slug"],
        stars: json["stars"],
        status: json["status"],
        parentId: json["parent_id"],
        catId: json["cat_id"],
        ownerId: json["owner_id"],
        owner: Owner.fromJson(json["owner"]),
        comments: List<Comment>.from(json["comments"].map((x) => Comment.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "governor": governor,
        "directorate": directorate,
        "address_details": addressDetails,
        "des": des,
        "whatsapp_number": whatsappNumber,
        "cover_image": coverImage,
        "slug": slug,
        "stars": stars,
        "status": status,
        "parent_id": parentId,
        "cat_id": catId,
        "owner_id": ownerId,
        "owner": owner.toJson(),
        "comments": List<dynamic>.from(comments.map((x) => x.toJson())),
      };
}

class Owner {
  final int id;
  final String name;

  Owner({
    required this.id,
    required this.name,
  });

  factory Owner.fromJson(Map<String, dynamic> json) => Owner(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

class Comment {
  final int id;
  final String comment;
  final int placeId;
  final int userId;
  final int? parentId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Reply? replies;
  final User user;

  Comment({
    required this.id,
    required this.comment,
    required this.placeId,
    required this.userId,
    this.parentId,
    required this.createdAt,
    required this.updatedAt,
    this.replies,
    required this.user,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => Comment(
        id: json["id"],
        comment: json["comment"],
        placeId: json["place_id"],
        userId: json["user_id"],
        parentId: json["parent_id"],
        createdAt: DateTime.parse(json["created_at"]),
        updatedAt: DateTime.parse(json["updated_at"]),
        replies: json["replies"] == null ? null : Reply.fromJson(json["replies"]),
        user: User.fromJson(json["user"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "comment": comment,
        "place_id": placeId,
        "user_id": userId,
        "parent_id": parentId,
        "created_at": createdAt.toIso8601String(),
        "updated_at": updatedAt.toIso8601String(),
        "replies": replies?.toJson(),
        "user": user.toJson(),
      };
}

class Reply {
  final int id;
  final String comment;
  final int placeId;
  final int userId;
  final int parentId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final User user;

  Reply({
    required this.id,
    required this.comment,
    required this.placeId,
    required this.userId,
    required this.parentId,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
  });

  factory Reply.fromJson(Map<String, dynamic> json) => Reply(
        id: json["id"],
        comment: json["comment"],
        placeId: json["place_id"],
        userId: json["user_id"],
        parentId: json["parent_id"],
        createdAt: DateTime.parse(json["created_at"]),
        updatedAt: DateTime.parse(json["updated_at"]),
        user: User.fromJson(json["user"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "comment": comment,
        "place_id": placeId,
        "user_id": userId,
        "parent_id": parentId,
        "created_at": createdAt.toIso8601String(),
        "updated_at": updatedAt.toIso8601String(),
        "user": user.toJson(),
      };
}

class User {
  final int id;
  final String name;

  User({
    required this.id,
    required this.name,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}
