import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/main.dart';

/// API server class that handles HTTP requests using GetConnect
class ApiServer extends GetConnect {
  /// Generic method for handling both GET and POST requests
  /// Returns response data for further processing
  Future<dynamic> getOrPostMethodData({
    Map<String, String>? headers, // Custom headers for the request
    bool isGet = true, // Flag to determine request type (GET/POST)
    required String url, // API endpoint URL
    dynamic body, // Request body for POST requests
    Map<String, dynamic>? queryParameters, // URL query parameters
    String? contentType, // Content type for POST requests
  }) async {
    // Debug: Print current authentication token
    debugPrint(box.read('token').toString());
    
    // Execute GET or POST request based on isGet flag
    var response = isGet
        ? await get(url, headers: headers, query: queryParameters)
        : await post(url, body,
            contentType: contentType, headers: headers, query: queryParameters);
    
    // Debug: Print the requested URL
    debugPrint(url);
    // debugPrint(response.body);

    return response;
  }
}
