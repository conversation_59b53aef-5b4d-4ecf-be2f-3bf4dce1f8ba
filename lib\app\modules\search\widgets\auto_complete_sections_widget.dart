import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/search/controller/search_controllers.dart';

class AutocompleteSearchBySectionWidget extends StatelessWidget {
  AutocompleteSearchBySectionWidget({
    super.key,
    this.suffixIcon,
  });
  final Widget? suffixIcon;

  final SearchPlaceController searchPlaceController =
      Get.find<SearchPlaceController>();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40.h,
      child: Autocomplete<String>(
        optionsBuilder: (textEditingValue) {
          final query = textEditingValue.text.toLowerCase();
          final sections = searchPlaceController.sectionsData.value.data;
          return sections
                  ?.where(
                    (section) => section.name!.toLowerCase().contains(query),
                  )
                  .map(
                    (section) => section.name!,
                  )
                  .toList() ??
              [];
        },
        onSelected: (String selectedValue) async {
          // Find the selected section by its name
          final selectedSection = searchPlaceController.sectionsData.value.data!
              .firstWhere((section) => section.name == selectedValue,
                  orElse: () =>
                      searchPlaceController.sectionsData.value.data!.first);
          searchPlaceController.onSectionChanged(selectedSection.id);
        },
        fieldViewBuilder: (context, controller, focusNode, onFieldSubmitted) {
          return TextFormField(
            controller: controller,
            focusNode: focusNode,
            decoration: InputDecoration(
              suffixIcon: suffixIcon,
              contentPadding: EdgeInsets.symmetric(horizontal: 10.w),
              hintText: 'الأقسام',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          );
        },
      ),
    );
  }
}
  // Obx(() {
          //   if (placeController.categoriesData?.value.data == null) {
          //     return const CircularProgressIndicator();
          //   }
          //   return DropDownFilter(
          //     hint: 'الفئات',
          //     onChanged: (newValue) {
          //       placeController.onCategoryChanged(newValue);
          //     },
          //     value: placeController.selectedCategory.value,
          //     items:
          //         placeController.categoriesData?.value.data?.map((category) {
          //               return DropdownMenuItem<int>(
          //                 value: category.id,
          //                 child: Text(category.name),
          //               );
          //             }).toList() ??
          //             [],
          //   );
          // }) 