import 'package:flutter/material.dart';

// Centralized color constants for consistent app theming
class AppColor {
  static const Color primaryColor = Color(0xff06B6D4);
  static const Color favColor = Color(0xFFFFFFFF);
  static const Color primaryColor1 = Color(0xffFFF4F1);
  static const Color primaryBackgroundColor = Color(0xffFFFFFF);
  static const Color primaryBack = Color(0xfff1f5f9);
  static const Color titleTextColor = Color(0xff1F1F39);
  static const Color textColor = Color(0xff1F1F39);
  static const Color textColor1 = Color(0xff6E7191);
  static const Color whiteColor = Color(0xffFFFFFF);
  static const Color blackColor = Color(0xff000000);
  static const Color blackColor1 = Color(0xff1F1F39);
  static const Color yellowColor = Color(0xffFFBC1F);
  static const Color yellowColor1 = Color(0xffF6A609);
  static const Color grayColor = Color(0xffD9DBE9);
  static const Color redColor = Color(0xffFF6262);
  static const Color redColor1 = Color(0xffF23E14);
  static const Color redColor2 = Color(0xffFB4E4E);
  static const Color blueColor = Color(0xff1F1F39);
  static const Color blueColor1 = Color(0xff0072F4);
  static const Color deSelectedColor = Color(0xff6E7191);
  static const Color borderColor = Color(0xffEFF0F6);
  static const Color orangeColor = Color(0xffF23E14);
  static const Color cartColor = Color(0xffF7F7FC);
  static const Color removeColor = Color(0xffFFF4F4);
  static const Color removeTextColor = Color(0xffE93C3C);
  static const Color pinkColor = Color(0xffFD0063);
  static const Color returnColor = Color(0xff9353DE);
  static const Color greenColor = Color(0xff1AB759);
  static const Color greenColor1 = Color(0xffD3FFE5);
  static const Color onthewayColor = Color(0xff007FE3);
  static const Color pendingColor = Color(0xffF6A609);
  static const Color paidColor = Color(0xffD3FFE5);
  static const Color unpaidColor = Color(0xffFFE8E8);
  static const Color refundColor = Color(0xffEAF6FF);
  static const Color refundTextColor = Color(0xff007FE3);
  static const Color activeColor = Color(0xff2AC769);
  static const Color inactiveColor = Color(0xffD9DBE9);
  static const Color starColor = Color(0xffF6A609);
  static const Color selectDeliveyColor = Color(0xffEAF6FF);
  static const Color deliveryColor = Color(0xff007FE3);
  static const Color editColor = Color(0xffE6FFF0);
  static const Color addressColor = Color(0xffF7F7F7);
  static const Color blueBorderColor = Color(0xff006CC0);
  static const Color applyCouponColor = Color(0xff007FE3);
  static const Color couponColor = Color(0xffF6FBFF);
  static const Color success = Color(0xff2AC769);
  static const Color error = Color(0xffFB4E4E);
  static const Color quantityError = Color(0xffA0A3BD);
}
