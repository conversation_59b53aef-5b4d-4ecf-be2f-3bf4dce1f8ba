import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/my_offers/controller/send_reply_controller.dart';
import 'package:ma3ak/app/modules/offers/models/place_information_old_model.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/widgets/widgets/send_comment.dart';

class CommentsPlaceOwnerTestPassAll extends StatelessWidget {
  CommentsPlaceOwnerTestPassAll(
      {super.key, this.placeComments, required this.slugPlace});
  final List<Comments>? placeComments;
  final String slugPlace;
  
  final SendReplyController sendReplyController =
      Get.find<SendReplyController>();
  final PlaceController placeController = Get.find<PlaceController>();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (placeController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }
      return ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.only(bottom: 50.h),
        shrinkWrap: true,
        itemBuilder: (context, index) {
          var comment = placeComments![index];
          // var comment = placeDetails!.comments[index];

          return Column(
            children: [
              ListTile(
                contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
                leading: CircleAvatar(
                  radius: 20.r,
                ),
                title: Text(comment.user!.name!),
                subtitle: Text(comment.comment!),
              ),
              comment.replies?.comment != null
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: RichText(
                            text: TextSpan(
                              text: 'الرد على ', // Default text
                              style: const TextStyle(
                                  color: Colors
                                      .black), // Default color for Arabic text
                              children: <TextSpan>[
                                TextSpan(
                                  text:
                                      '${comment.user!.name!} ', // Name in blue
                                  style: const TextStyle(
                                      color: Colors
                                          .blue), // Blue color for the name
                                ),
                                const TextSpan(
                                  text: ':',
                                  style: TextStyle(
                                      color: Colors
                                          .black), //// Colon after the name
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          child: Row(
                            children: [
                              // Adding some space between avatar and text
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      comment.replies?.user?.name ?? '',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    Text(comment.replies?.comment ?? ''),
                                  ],
                                ),
                              ),
                              SizedBox(width: 8.w),
                              CircleAvatar(
                                radius: 16.r,
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  : Obx(
                      () => TextButton(
                        onPressed: () {
                          sendReplyController.toggleReply(index);
                          showBottomSheet(
                            context: context,
                            builder: (context) {
                              return Obx(() {
                                if (sendReplyController
                                        .activeReplyIndex.value ==
                                    index) {
                                  return Obx(
                                    () => SendComment(
                                      hintText: 'أضف رد',
                                      onChanged: (value) {
                                        sendReplyController.sendText.value =
                                            value;
                                      },
                                      sendCommentcontroller: sendReplyController
                                          .sendTextController,
                                      onPressed:
                                          sendReplyController.sendText.value ==
                                                  ''
                                              ? null
                                              : () async {
                                                  await sendReplyController
                                                      .sendReply(
                                                    commentId: comment.id!,
                                                  );
                                                  await placeController
                                                      .getPlaceInforamtion(
                                                          slugPlace: slugPlace);
                                                },
                                    ),
                                  );
                                }
                                return const SizedBox(); // If this comment is not selected, return an empty SizedBox
                              });
                            },
                          );
                        },
                        child:
                            sendReplyController.activeReplyIndex.value == index
                                ? const Text('إلغاء الرد')
                                : Text(
                                    'الرد',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      // color: AppColor.blueColor1),
                                    ),
                                  ),
                      ),
                    ),
            ],
          );
        },
        separatorBuilder: (context, index) {
          return SizedBox(
            height: 10.h,
          );
        },
        itemCount: placeComments?.length ?? 0,
      );
    });
  }
}
