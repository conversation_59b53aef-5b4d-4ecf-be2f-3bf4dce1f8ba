import 'package:flutter/material.dart';
import 'package:ma3ak/config/theme/app_color.dart';

// Reusable basic button widget with customizable width, text, and color
class BasicButton extends StatelessWidget {
  const BasicButton({
    super.key,
    required this.wdth,
    required this.text,
    this.onPressed,
    this.color = AppColor.blueColor1,
  });

  // Button properties
  final double wdth;
  final String text;
  final void Function()? onPressed;
  final Color? color;
  
  // Builds the button widget with custom styling
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: wdth * 0.7,
      child: ElevatedButton(
        style: ButtonStyle(
            shape: const WidgetStatePropertyAll<OutlinedBorder>(
                ContinuousRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(15),
              ),
            )),
            backgroundColor: WidgetStatePropertyAll<Color>(color!)),
        onPressed: onPressed,
        child: Text(
          text,
          style: const TextStyle(color: AppColor.whiteColor),
        ),
        // style: ButtonStyle(w)
      ),
    );
  }
}
