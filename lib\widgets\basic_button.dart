import 'package:flutter/material.dart';
import 'package:ma3ak/config/theme/app_color.dart';

/// Reusable basic button widget with customizable width, text, and color
/// Provides consistent button styling across the application
class BasicButton extends StatelessWidget {
  const BasicButton({
    super.key,
    required this.wdth,
    required this.text,
    this.onPressed,
    this.color = AppColor.blueColor1,
  });

  // Button configuration properties
  final double wdth;                    // Base width for responsive sizing
  final String text;                    // Button label text
  final void Function()? onPressed;     // Tap callback function
  final Color? color;                   // Background color (defaults to blue)

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      // Responsive width calculation (70% of provided width)
      width: wdth * 0.7,
      child: ElevatedButton(
        // Custom button styling with rounded corners
        style: ButtonStyle(
            shape: const WidgetStatePropertyAll<OutlinedBorder>(
                ContinuousRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(15), // Rounded corners for modern look
              ),
            )),
            backgroundColor: WidgetStatePropertyAll<Color>(color!)),
        onPressed: onPressed,
        child: Text(
          text,
          // White text for contrast against colored background
          style: const TextStyle(color: AppColor.whiteColor),
        ),
      ),
    );
  }
}
