import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/add_or_remove_fav_model.dart';
import 'package:ma3ak/app/modules/place/models/place_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

// Controller for managing user's favorite places
class FavoriteController extends GetxController {
  // Loading state and data models for favorites
  RxBool isLoading = false.obs;
  Rx<PlaceModel> favoriteData = PlaceModel().obs;
  RxList<PlaceDatum>? favoritePlaceList = <PlaceDatum>[].obs;
  Rx<AddOrRemoveFavoriteModel> addOrRemoveFavoriteModel =
      AddOrRemoveFavoriteModel().obs;

  // Fetches user's favorite places from API
  Future<void> getFavoriteUser() async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .getFavoriteUser(); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      favoriteData.value = dataModel;
      favoritePlaceList?.value = dataModel.data!;
      debugPrint(favoriteData.value.data.toString());
    });
  }

  // Adds or removes a place from user's favorites
  Future<void> addOrRemoveFavorite({required String slugPlace}) async {
    isLoading(true);
    final data = await RemoteServicesImpl().addOrRemoveFavorite(
        slugPlace: slugPlace); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      addOrRemoveFavoriteModel.value = dataModel;
      refreshFavorite();
      debugPrint(favoriteData.value.data.toString());
    });
  }

  // Refreshes the favorite places list
  void refreshFavorite() async {
    await getFavoriteUser();
    favoritePlaceList?.refresh();
  }

  // Initializes controller by fetching favorite places
  @override
  void onInit() async {
    await getFavoriteUser(); // Fetch ads on initialization
    super.onInit();
  }
}
