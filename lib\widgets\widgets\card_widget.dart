import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating/flutter_rating.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ma3ak/app/modules/home/<USER>/icon_with_text.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/main.dart';

/// Reusable card widget for displaying place information with image, rating, and location
/// Shows place details in a horizontal card layout with favorite and edit functionality
class CardWidget extends StatelessWidget {
  const CardWidget({
    super.key,
    this.isFave = false,
    this.onPressed,
    required this.title,
    required this.rating,
    required this.imgUrl,
    this.onPressedEdit,
    this.showIsEdit = false,
    required this.governor,
    required this.directorate,
  });

  // Card configuration properties and interaction callbacks
  final bool? isFave;                   // Current favorite status of the place
  final void Function()? onPressed;     // Callback for favorite button tap
  final void Function()? onPressedEdit; // Callback for edit button tap
  final String title;                   // Place name/title to display
  final double rating;                  // Star rating value (0-5)
  final String imgUrl;                  // Place image URL for display
  final String governor;                // Governor/state location
  final String directorate;             // Directorate/city location
  final bool? showIsEdit;               // Whether to show edit button (for owners)
  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      child: SizedBox(
        // Responsive height based on screen size
        height: MediaQuery.of(context).size.height * .15,
        child: Row(
          children: [
            // Left section - Place image with caching for performance
            Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CachedNetworkImage(
                    imageUrl: ApiList.baseUrl + imgUrl,
                    imageBuilder: (context, imageProvider) => Container(
                      decoration: BoxDecoration(
                        // color: Colors.grey,
                        borderRadius: BorderRadius.circular(8.r),
                        image: DecorationImage(
                            image: imageProvider, fit: BoxFit.contain),
                      ),
                    ),
                  ),
                )),
            // Right section - Place details and actions
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.only(right: 0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title row with action button (edit/favorite)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            // Place title
                            Expanded(
                              child: Text(
                                title,
                                style:  TextStyle(fontSize: 12.sp,fontWeight: FontWeight.bold),
                              ),
                            ),
                            // Conditional action button based on user type and authentication
                            box.read('token') != null
                                ? box.read('isOwner') == 1
                                    ? showIsEdit == true
                                        ? TextButton(
                                            onPressed: onPressedEdit,
                                            child: const Text('تعديل'))
                                        : const SizedBox()
                                    : IconButton(
                                        onPressed: onPressed,
                                        icon: const Icon(
                                          Icons.favorite_border,
                                        ),
                                      )
                                : IconButton(
                                    onPressed: onPressed,
                                    icon: const Icon(
                                      Icons.favorite_border,
                                    ),
                                  )
                          ],
                        ),
                      ],
                    ),
                    // Star rating display
                    StarRating(
                      size: 15.w,
                      mainAxisAlignment: MainAxisAlignment.start,
                      allowHalfRating: true,
                      color: Colors.amber,
                      rating: rating,
                    ),
                   
                    // Location information with icon
                    IconWithText(
                      title: '$governor - $directorate',
                      icon: Icons.location_on_outlined,
                    ),
                    
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
