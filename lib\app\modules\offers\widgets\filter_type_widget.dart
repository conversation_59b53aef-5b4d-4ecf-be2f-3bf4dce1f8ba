import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/offers/widgets/drop_down_filter_types.dart';
import 'package:ma3ak/config/theme/app_color.dart';

class FilterTypeWidget extends StatelessWidget {
  FilterTypeWidget({
    super.key,
  });
  final OffersController offersController = Get.find<OffersController>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Container(
            height: 40.h,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
                color: AppColor.grayColor,
                borderRadius: BorderRadius.only(topLeft: Radius.circular(4.r))),
            child: const Icon(
              Icons.filter_alt_outlined,
            ),
          ),
          const SizedBox(
            width: 5,
          ),
          Obx(() {
            // if (offersController.sectionsData.value.data == null) {
            //   return const CircularProgressIndicator();
            // }
            return DropDownFilterTypes(
              hint: 'نوع العرض',
              onChanged: (String? newValue) {
                offersController.selectedType.value = newValue!;
              },
              value: offersController.selectedType.value.isEmpty
                  ? null
                  : offersController.selectedType.value,
              items: offersController.types
                  .map((type) => DropdownMenuItem<String>(
                        value: type,
                        onTap: () async {
                          await offersController.fetchOffersByType(type: type);
                        },
                        child: Text(type),
                      ))
                  .toList(),
            );
          }),
          // Obx(() {
          //   if (placeController.categoriesData?.value.data == null) {
          //     return const CircularProgressIndicator();
          //   }
          //   return DropDownFilter(
          //     hint: 'الفئات',
          //     onChanged: (newValue) {
          //       placeController.onCategoryChanged(newValue);
          //     },
          //     value: placeController.selectedCategory.value,
          //     items:
          //         placeController.categoriesData?.value.data?.map((category) {
          //               return DropdownMenuItem<int>(
          //                 value: category.id,
          //                 child: Text(category.name),
          //               );
          //             }).toList() ??
          //             [],
          //   );
          // })
          Obx(() {
            if (offersController.selectedType.value.isNotEmpty) {
              return IconButton(
                  onPressed: () async {
                    offersController.selectedType.value = '';
                    await offersController.fetchOffers();
                  },
                  icon: const Icon(Icons.close));
            }
            return const SizedBox();
          })
        ],
      ),
    );
  }
}
