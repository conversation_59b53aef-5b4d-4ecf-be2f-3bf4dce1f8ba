/// Model representing a card item in search results with place details
class CardModel {
  // Title/name of the place or offer
  final String title;
  
  // URL for the place's image
  final String imageUrl;
  
  // Rating score for the place (0.0 to 5.0)
  final double rating;
  
  // Location/address of the place (optional)
  final String? location;
  
  // Favorite status - mutable to allow toggling
  bool isFav;
  
  // Additional information as key-value pairs
  final List<Map<String, dynamic>>? info;

  // Constructor with required and optional parameters
  CardModel( {
    this.location,
    required this.title,
    required this.imageUrl,
    required this.rating,
    this.isFav = false, // Default to not favorited
    required this.info,
  });
}
