import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Controller for managing login form state and interactions
class LoginController extends GetxController {
  // Observable for password field visibility toggle
  var isPasswordVisible = false.obs;
  
  // Observable for remember me checkbox state
  var isRememberMeChecked = false.obs;
  
  // Form key for validation
  final formKey = GlobalKey<FormState>(); 

  /// Toggles password visibility between hidden and visible
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  /// Toggles remember me checkbox state
  void toggleRememberMe() {
    isRememberMeChecked.value = !isRememberMeChecked.value;
  }
}
