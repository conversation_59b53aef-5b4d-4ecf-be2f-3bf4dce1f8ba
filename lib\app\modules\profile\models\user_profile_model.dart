import 'dart:convert';

// To parse this JSON data, do
//
//     final userProfileModel = userProfileModelFromJson(jsonString);

UserProfileModel userProfileModelFromJson(String str) =>
    UserProfileModel.fromJson(json.decode(str));

String userProfileModelToJson(UserProfileModel data) =>
    json.encode(data.toJson());

class UserProfileModel {
  final bool? status;  // Changed to nullable
  final String? message; // Changed to nullable
  final User? user; // User object can be null

  UserProfileModel({
    this.status,
    this.message,
    this.user,
  });

  factory UserProfileModel.fromJson(Map<String, dynamic> json) =>
      UserProfileModel(
        status: json["status"],
        message: json["message"],
        user: json["date"] != null ? User.fromJson(json["date"]) : null, // Using "date" for user data
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "date": user?.toJson(), // Convert user to JSON if it's not null
      };
}

class User {
  final int? id; // Changed to nullable
  final String? name; // Changed to nullable
  final String? email; // Changed to nullable
  final String? phoneNumber; // Changed to nullable
  final int? isOwner; // Changed to nullable
  final String? slug; // Changed to nullable
  final String? img; // Changed to nullable
  final String? emailVerifiedAt; // Remains nullable
  final String? status; // Changed to nullable
  final DateTime? createdAt; // Changed to nullable
  final DateTime? updatedAt; // Changed to nullable

  User({
    this.id,
    this.name,
    this.email,
    this.phoneNumber,
    this.isOwner,
    this.slug,
    this.img,
    this.emailVerifiedAt,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"],
        name: json["name"],
        email: json["email"],
        phoneNumber: json["phone_number"],
        isOwner: json["isOwner"],
        slug: json["slug"],
        img: json["img"],
        emailVerifiedAt: json["email_verified_at"],
        status: json["status"],
        createdAt: json["created_at"] != null ? DateTime.parse(json["created_at"]) : null,
        updatedAt: json["updated_at"] != null ? DateTime.parse(json["updated_at"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "email": email,
        "phone_number": phoneNumber,
        "isOwner": isOwner,
        "slug": slug,
        "img": img,
        "email_verified_at": emailVerifiedAt,
        "status": status,
        "created_at": createdAt?.toIso8601String(), // Convert to string if not null
        "updated_at": updatedAt?.toIso8601String(), // Convert to string if not null
      };
}
