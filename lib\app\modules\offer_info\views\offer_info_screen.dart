import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/offers/widgets/icon_button_outlined_with_text_widget.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/main.dart';

class OfferInfoScreen extends StatelessWidget {
  const OfferInfoScreen({
    super.key,
    required this.namePlace,
    required this.title,
    required this.img,
    required this.type,
    required this.des,
    required this.slugOffer,
    required this.slugPlace,
    required this.likes,
    required this.disLikes,
  });
  final String namePlace;
  final String slugPlace;
  final String slugOffer;
  final String title;
  final String img;
  final String type;
  final String des;
  final String likes;
  final String disLikes;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(namePlace),
      ),
      body: Column(
        children: [
          CachedNetworkImage(
            fit: BoxFit.contain,
            height: MediaQuery.of(context).size.height * .4,
            width: double.infinity,
            imageUrl: ApiList.baseUrl + img,
            errorWidget: (context, url, error) => const Icon(Icons.error),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 10,
                ),
                Row(
                  children: [
                    const Text(
                      'إسم العرض :',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                Row(
                  children: [
                    const Text(
                      'النوع :',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(type)
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                const Text(
                  'الوصف :',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(des),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  children: [
                    IconButtonOutlinedWithTextWidget(
                      number: likes,
                      onPressed: box.read('token') == '' ||
                              box.read('token') == null
                          ? () {
                              Get.snackbar(
                                'ليس لديك صلاحية لتقيم العرض',
                                'الرجاء تسجيل الدخول أولاً',
                                backgroundColor: Colors.green,
                                colorText: Colors.white,
                                icon: const Icon(
                                  Icons.warning,
                                  color: Colors.white,
                                ),
                              );
                            }
                          : box.read('isOwner') == 1
                              ? null
                              : () async {
                                  var res = await Get.find<OffersController>()
                                      .likeOffer(slugOffer: slugOffer);
                                  res.fold((error) {
                                    Get.snackbar('Error', error.toString());
                                  }, (dataModel) async {
                                    await Get.find<OffersController>()
                                        .fetchOffersByPlace(
                                            slugPlace: slugPlace);
                                    Get.snackbar(
                                      'نجاح',
                                      'تم التقيم بنجاح  عليك بالرجوع لكي ترى التقيم ',
                                      backgroundColor: Colors.green,
                                      colorText: Colors.white,
                                      icon: const Icon(
                                        Icons.check_circle,
                                        color: Colors.white,
                                      ),
                                    );
                                  });
                                },
                      icon: Icons.thumb_up_alt_outlined,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    IconButtonOutlinedWithTextWidget(
                      number: disLikes,
                      onPressed: box.read('token') == '' ||
                              box.read('token') == null
                          ? () {
                              Get.snackbar(
                                'ليس لديك صلاحية لتقيم العرض',
                                'الرجاء تسجيل الدخول أولاً',
                                backgroundColor: Colors.green,
                                colorText: Colors.white,
                                icon: const Icon(
                                  Icons.warning,
                                  color: Colors.white,
                                ),
                              );
                            }
                          : box.read('isOwner') == 1
                              ? null
                              : () async {
                                  var res = await Get.find<OffersController>()
                                      .disLikeOffer(slugOffer: slugOffer);

                                  res.fold((error) {
                                    Get.snackbar('Error', error.toString());
                                  }, (dataModel) async {
                                    await Get.find<OffersController>()
                                        .fetchOffersByPlace(
                                            slugPlace: slugPlace);
                                    Get.snackbar(
                                      'نجاح',
                                      'تم التقيم بنجاح  عليك بالرجوع لكي ترى التقيم ',
                                      backgroundColor: Colors.green,
                                      colorText: Colors.white,
                                      icon: const Icon(
                                        Icons.check_circle,
                                        color: Colors.white,
                                      ),
                                    );
                                  });
                                },
                      icon: Icons.thumb_down_alt_outlined,
                    ),
                  ],
                ),
                const SizedBox(
                  width: 10,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
