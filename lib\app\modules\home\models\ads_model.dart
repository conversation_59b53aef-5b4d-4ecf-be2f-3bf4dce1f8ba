// Data model for advertisements API response
class AdModel {
  bool? status;
  String? message;
  List<Data>? data;

  AdModel({this.status, this.message, this.data});

  // Creates AdModel from JSON response
  AdModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }

  // Converts AdModel to JSON format
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

// Data model for individual advertisement
class Data {
  String? img;

  Data({this.img});

  // Creates Data from JSON response
  Data.fromJson(Map<String, dynamic> json) {
    img = json['img'];
  }

  // Converts Data to JSON format
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['img'] = img;
    return data;
  }
}
