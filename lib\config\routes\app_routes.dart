/// Centralized route constants for app navigation
/// Provides string constants for all application routes to ensure consistency
class Routes {
  Routes._();

  // Core navigation routes
  static const user = '/user/';                                    // User profile and settings
  static const navBarView = '/navBarView/';                        // Main navigation with bottom bar
  static const home = '/home/';                                    // Home screen

  // Content browsing routes
  static const categoryView = '/categoryView/';                    // Category listing
  static const subCategory = '/subCategory/';                      // Subcategory view
  static const categoryWiseProductView = "/categoryWiseProductView/"; // Category-filtered products

  // User interaction routes
  static const whishlistView = '/whishlistView/';                  // User favorites/wishlist
  static const profileView = '/profileView/';                      // User profile management
  static const cartScreen = "/cartScreen/";                        // Shopping cart

  // Administrative routes
  static const updatePlace = "/updatePlace/";                      // Place editing for owners
  static const language = '/language/';                            // Language selection
}
