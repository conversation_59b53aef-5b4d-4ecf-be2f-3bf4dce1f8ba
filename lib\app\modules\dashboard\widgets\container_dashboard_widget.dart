import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ma3ak/utils/svg_icon.dart';

class ContainerDashboardWidget extends StatelessWidget {
  const ContainerDashboardWidget({
    super.key,
    this.text = 'التعليقات',
    this.svgIcon = SvgIcon.comments,
    this.textUnder = 'مجموع التعليقات',
    this.number = '0', this.onTap,
  });
  final String? text;
  final String? textUnder;
  final String? svgIcon;
  final String? number;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: InkWell(
        borderRadius: BorderRadius.circular(8.r),
        onTap:onTap,
        child: Container(
          height: MediaQuery.of(context).size.height * .2,
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey), // Border color
            borderRadius: BorderRadius.circular(8.r), // Circular border
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text(
                    text!,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  Container(
                    padding: const EdgeInsets.all(8.0),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6.r),
                        color: const Color(0xffF3E8FF)),
                    child: SvgPicture.asset(
                      height: 18,
                      width: 18,
                      svgIcon!,
                    ),
                  ),
                ],
              ),
              Padding(
                  padding: EdgeInsets.only(top: 20.h),
                  child: RichText(
                      text: TextSpan(
                    children: [
                      TextSpan(
                        style: TextStyle(fontSize: 16.sp, color: Colors.black),
                        text: textUnder!,
                      ),
                      const TextSpan(text: '  '),
                      TextSpan(
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.blue,
                        ),
                        text: number,
                      ),
                    ],
                  )))
            ],
          ),
        ),
      ),
    );
  }
}
