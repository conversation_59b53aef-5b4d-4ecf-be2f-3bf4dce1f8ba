import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DropDownFilterTypes extends StatelessWidget {
  const DropDownFilterTypes({
    super.key,
    this.onChanged,
    this.items,
    this.value,
    this.hint = 'Filter',
  });
  final void Function(String?)? onChanged;
  final List<DropdownMenuItem<String>>? items;
  final String? value;
  final String? hint;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 5),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        height: 40.h,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey), // Border color
          borderRadius: BorderRadius.circular(8.r), // Circular border
        ),
        child: DropdownButton<String>(
          hint: Text(hint!),
          icon: const Icon(Icons.expand_more),
          padding: EdgeInsets.zero,
          underline: const SizedBox(),
          value: value,
          items: items,
          onChanged: onChanged,
        ),
      ),
    );
  }
}
