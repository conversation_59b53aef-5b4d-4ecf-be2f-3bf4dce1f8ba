// Data model for user authentication response
class UserModel {
  User? user;
  String? token;
  int? isOwner;

  UserModel({this.user, this.token, this.isOwner});

  // Creates UserModel from JSON response
  UserModel.fromJson(Map<String, dynamic> json) {
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    token = json['token'];
    isOwner = json['isOwner'];
  }

  // Converts UserModel to JSON format
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (user != null) {
      data['user'] = user!.toJson();
    }
    data['token'] = token;
    data['isOwner'] = isOwner;
    return data;
  }
}

// Data model for user information
class User {
  int? id;
  String? name;
  String? email;
  String? phoneNumber;
  int? isOwner;
  String? slug;
  String? img;
  Null emailVerifiedAt;
  String? status;
  String? createdAt;
  String? updatedAt;

  User(
      {this.id,
      this.name,
      this.email,
      this.phoneNumber,
      this.isOwner,
      this.slug,
      this.img,
      this.emailVerifiedAt,
      this.status,
      this.createdAt,
      this.updatedAt});

  // Creates User from JSON data
  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phoneNumber = json['phone_number'];
    isOwner = json['isOwner'];
    slug = json['slug'];
    img = json['img'];
    emailVerifiedAt = json['email_verified_at'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  // Converts User to JSON format
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['phone_number'] = phoneNumber;
    data['isOwner'] = isOwner;
    data['slug'] = slug;
    data['img'] = img;
    data['email_verified_at'] = emailVerifiedAt;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
