import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/my_offers/controller/send_reply_controller.dart';
import 'package:ma3ak/app/modules/offers/models/place_information_old_model.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/place/widgets/comments_place_owner_test_pass_all.dart';
import 'package:ma3ak/app/modules/place/widgets/current_offers_in_place_pass_all.dart';
import 'package:ma3ak/app/modules/place/widgets/description_details_widget_test.dart';
import 'package:ma3ak/app/modules/place/widgets/name_location_rating_widget_test.dart';
import 'package:ma3ak/app/modules/place/widgets/open_whats_app_number.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/data/constants/api_list.dart';

class PlaceScreenOwner extends StatelessWidget {
  const PlaceScreenOwner({
    super.key,
    required this.placeInfromation,
  });
  final Rx<PlaceInfromationOldModel> placeInfromation;

  @override
  Widget build(BuildContext context) {
    Get.lazyPut(() => SendReplyController());
    var placeInfo = placeInfromation.value.data;
    // var placeComment = placeInfromation.value.data.;
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return [
            SliverAppBar(
              backgroundColor: AppColor.primaryColor,
              expandedHeight: 300.h,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                background: CachedNetworkImage(
                  fit: BoxFit.cover,
                  imageUrl: ApiList.baseUrl + placeInfo.coverImage!,
                  imageBuilder: (context, imageProvider) => Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: Colors.white,
                      image: DecorationImage(
                        fit: BoxFit.contain,
                        image: imageProvider,
                      ),
                    ),
                  ),
                ),
                centerTitle: true,
                // title: Text(placeInfo.name!,
                //     style: const TextStyle(
                //       color: Colors.white,
                //     )),
              ),
            ),
          ];
        },
        body: RefreshIndicator(
          onRefresh: () async {
            Get.find<PlaceController>()
                .getPlaceInforamtion(slugPlace: placeInfo.slug!);
          },
          child: ListView(
            children: [
              NameLocationRatingWidgetTest(
                slugPlace: placeInfo!.slug!,
                namePlace: placeInfo.name!,
                governor: placeInfo.governor!,
                directorate: placeInfo.directorate!,
                addressDetails: placeInfo.addressDetails!,
                ratingPlace: placeInfo.stars!.toDouble(),
              ),
              DescriptionDetailsWidgetTest(des: placeInfo.des!),
              OpenWhatsAppNumber(
                  phoneNumber: placeInfo.whatsappNumber.toString()),
              CurrentOffersInPlacePassAll(
                slugPlace: placeInfo.slug ?? '',
                namePlace: placeInfo.name ?? '',
              ),
              CommentsPlaceOwnerTestPassAll(
                  slugPlace: placeInfo.slug!,
                  placeComments: placeInfo.comments),
              const SizedBox(
                height: 70,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
