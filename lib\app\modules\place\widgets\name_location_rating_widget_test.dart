import 'package:flutter/material.dart';
import 'package:flutter_rating/flutter_rating.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/place/controller/send_rating_controller.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/basic_button.dart';

class NameLocationRatingWidgetTest extends StatelessWidget {
  NameLocationRatingWidgetTest({
    super.key,
    required this.namePlace,
    required this.governor,
    required this.directorate,
    required this.addressDetails,
    required this.ratingPlace,
    required this.slugPlace,
  });
  final sendRatingController = Get.put(SendRatingController());

  final String namePlace;
  final String slugPlace;
  final String governor;
  final String directorate;
  final String addressDetails;
  final double ratingPlace;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
        decoration: BoxDecoration(
          color: AppColor.primaryColor.withOpacity(.2),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    namePlace,
                    style:
                        TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '$governor - $directorate - $addressDetails',
                    style: TextStyle(
                      fontSize: 10.sp,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              children: [
                InkWell(
                  onTap: () {
                    if (box.read('token')!=null && box.read('isOwner')==0) {
                       Get.dialog(
                      AlertDialog(
                          title: const Text('التقييم'),
                          content: Wrap(
                            children: [
                              Obx(
                                () => StarRating(
                                  allowHalfRating: true,
                                  color: Colors.amber,
                                  rating: sendRatingController.rating.value,
                                  onRatingChanged: (newRating) {
                                    sendRatingController.rating.value =
                                        newRating;
                                  },
                                ),
                              ),
                            ],
                          ),
                          actions: [
                            BasicButton(
                                onPressed: () async {
                                  await sendRatingController.userRating(
                                      slugPlace: slugPlace,
                                      rating:
                                          sendRatingController.rating.value);
                                  await Get.find<PlaceController>()
                                      .getPlaceInforamtion(
                                          slugPlace: slugPlace);
                                  sendRatingController.rating(0);
                                },
                                wdth: double.infinity,
                                text: 'تقييم'),
                          ]),
                      barrierColor: Colors.transparent,
                    );
                  
                    }else{
                      Get.snackbar('الرسالة', 'يجب تسجيل الدخول اولا');
                    }

                   },
                  child: StarRating(
                    allowHalfRating: true,
                    color: Colors.amber,
                    rating: ratingPlace,
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
