import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Controller for managing bottom navigation bar and page selection
class NavbarController extends GetxController {
  // Currently selected tab index (default: 2)
  final selectedIndex = 2.obs;
  // Scroll controller for main content area
  final ScrollController scrollController = ScrollController();
  // Controls whether the screen can be popped
  RxBool canPop = false.obs;
  
  // Initialize controller and set up scroll listener
  @override
  void onInit() {
    super.onInit();
    scrollController.addListener(_onScroll);
  }

  // Updates selected tab index when user taps navigation item
  void selectPage(int index) {
    selectedIndex.value = index;
  }

  // Handles scroll events to trigger drawer opening
  void _onScroll() {
    if (scrollController.position.pixels > 100) {
      Get.context?.findAncestorStateOfType<ScaffoldState>()?.openDrawer();
    }
  }

  // Clean up resources when controller is disposed
  @override
  void onClose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.onClose();
  }
}
