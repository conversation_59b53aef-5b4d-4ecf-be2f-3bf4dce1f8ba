import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/place_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class MyPlaceController extends GetxController {
  var isLoading = false.obs;
  Rx<PlaceModel> myPlaceData = PlaceModel().obs;
  var selectedPlaceSlug = ''.obs;

  Future<void> fetchMyPlace() async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .fetchMyPlaces(); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      myPlaceData.value = dataModel;
      debugPrint(myPlaceData.value.toString());
    });
  }

  @override
  void onInit() {
    fetchMyPlace();
    super.onInit();
  }
}
