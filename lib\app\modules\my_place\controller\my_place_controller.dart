import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/place_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for managing user's owned places data and operations
class MyPlaceController extends GetxController {
  // Loading state indicator for API requests
  var isLoading = false.obs;

  // Observable container for user's places data
  Rx<PlaceModel> myPlaceData = PlaceModel().obs;

  // Currently selected place slug for operations
  var selectedPlaceSlug = ''.obs;

  /// Fetches all places owned by the current user
  Future<void> fetchMyPlace() async {
    isLoading(true);

    // Call API service to retrieve user's places
    final data = await RemoteServicesImpl()
        .fetchMyPlaces();
    isLoading(false);

    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      myPlaceData.value = dataModel;
      debugPrint(myPlaceData.value.toString());
    });
  }

  /// Initialize controller by fetching user's places on startup
  @override
  void onInit() {
    fetchMyPlace();
    super.onInit();
  }
}
