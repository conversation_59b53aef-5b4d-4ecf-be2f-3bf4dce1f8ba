import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class UpdateProfleController extends GetxController {
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
//  Rx<File?> imageFile = Rx<File?>(null);
  var selectedImagePath = ''.obs;

  final ImagePicker _imagePicker = ImagePicker();

  Future<void> pickOneImage() async {
    try {
      final pickedImage =
          await _imagePicker.pickImage(source: ImageSource.gallery);
      if (pickedImage != null) {
        selectedImagePath.value = pickedImage.path;
      } else {
        Get.snackbar('Error', 'No Image Selected');
      }
    } catch (e) {
      Get.snackbar('Error', e.toString());
    }
  }

  Future<void> updateUserProfile() async {
    final multipartFile = await convertFileToMultipartFile();
    final formData = FormData({
      'name': nameController.text,
      'email': emailController.text,
      'phone_number': phoneNumberController.text,
      'password': passwordController.text,
      'confirmPassword': confirmPasswordController.text,
      // 'img': multipartFile
    });
    if (multipartFile == null) {
      // Get.snackbar('Error', 'No Image Selected');
    } else {
      formData.files.add(
        MapEntry(
          'img',
          multipartFile,
        ),
      );
    }
    try {
      final response =
          await RemoteServicesImpl().updateUserProfile(body: formData);
      if (response["status"] == true) {
        Get.snackbar(
          "Success",
          "Profile updated user successfully",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        Future.delayed(const Duration(milliseconds: 100), () {
          Get.find<NavbarController>().selectedIndex.value = 0;
          Get.find<ProfileController>().getUserProfile();
          update();
          // Get.offAll(() => const NavBarView());
          Get.toNamed(Routes.navBarView); // Go back after the snackbar
        });
      } else if (response["status"] == false) {
        Get.snackbar(
          "Error status",
          "Failed to update profile. Please try again.",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // Handle any errors
      Get.snackbar(
        "Error",
        "An error occurred: $e",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<MultipartFile?> convertFileToMultipartFile() async {
    if (selectedImagePath.isNotEmpty) {
      return MultipartFile(
        selectedImagePath.value,
        filename: selectedImagePath.value.split('/').last,
        // contentType: "multipart/form-data",
      );
    }
    return null;
  }

  Future<void> updateOwnerProfile() async {
    final multipartFile = await convertFileToMultipartFile();
    final formData = FormData({
      'name': nameController.text,
      'email': emailController.text,
      'phone_number': phoneNumberController.text,
      'password': passwordController.text,
      'confirmPassword': confirmPasswordController.text,
      // 'img': multipartFile
    });
    if (multipartFile == null) {
      Get.snackbar('Error', 'No Image Selected');
    } else {
      formData.files.add(MapEntry('img', multipartFile));
    }
    try {
      final response =
          await RemoteServicesImpl().updateOwnerProfile(body: formData);
      if (response["status"] == true) {
        Get.snackbar(
          "Success",
          "Profile updated owner successfully",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        Future.delayed(const Duration(milliseconds: 100), () {
          Get.find<NavbarController>().selectedIndex.value = 0;
          Get.find<ProfileController>().getOwnerProfile();
          update();
          // Get.offAll(() => const NavBarView());
          Get.toNamed(Routes.navBarView); // Go back after the snackbar
        });
      } else if (response["status"] == false) {
        Get.snackbar(
          "Error status",
          "Failed to update owner profile. Please try again.",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // Handle any errors
      Get.snackbar(
        "Error",
        "An error owner occurred: $e",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
