import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

/// Small shimmer loading placeholder for dropdown or button containers
/// Used to show loading state for compact UI elements
class ContainerDropShimmer extends StatelessWidget {
  const ContainerDropShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    // Small rectangular shimmer with fixed dimensions
    return Shimmer.fromColors(
      baseColor: Colors.grey[200]!,
      highlightColor: Colors.grey[300]!,
      child: Container(
        height: 40.h, // Compact height for dropdown-like elements
        width: 70.w,  // Small width for button-like elements
        color: Colors.white,
      ),
    );
  }
}
