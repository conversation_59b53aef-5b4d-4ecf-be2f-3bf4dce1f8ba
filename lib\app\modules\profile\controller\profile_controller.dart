import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/profile/models/owner_profile_model.dart';
import 'package:ma3ak/app/modules/profile/models/user_profile_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';
import 'package:ma3ak/main.dart';

// Controller for managing user and owner profile data and operations
class ProfileController extends GetxController {
  // Loading states for different profile types
  final isLoading = false.obs;
  final isLoadingOwner = false.obs;
  
  // User type flag and profile data containers
  RxBool isOwner = box.read('isOwner') == 1 ? true.obs : false.obs;
  final userProfileData = UserProfileModel().obs;
  final ownerProfileData = OwnerProfileModel().obs;

  // Fetches user profile data from API
  Future<void> getUserProfile() async {
    debugPrint("token  ${box.read("token")}");
    debugPrint("isOwner  ${box.read("isOwner").toString()}");
    isLoading(true);
    final data = await RemoteServicesImpl()
        .getProfileUser(); // Make sure this method exists
    isLoading(false);
    // Handle API response - either error or success
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      userProfileData.value = dataModel;
      // userProfileData.refresh();

      update();

      debugPrint(userProfileData.value.user?.email);
    });
  }

  // Fetches owner profile data from API
  Future<void> getOwnerProfile() async {
    debugPrint("token  ${box.read("token")}");
    debugPrint("isOwner  ${box.read("isOwner").toString()}");
    isLoadingOwner(true);
    update();
    final data = await RemoteServicesImpl()
        .getProfileOwner(box.read('token')); // Make sure this method exists
    isLoadingOwner(false);
    // Handle API response - either error or success
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      ownerProfileData.value = dataModel;
      update();
      debugPrint(userProfileData.value.user?.email);
    });
  }

  @override
  void onInit() async {
    if (box.read('token') != '') {
      if (box.read('isOwner') == 1) {
        await getOwnerProfile();
      } else {
        await getUserProfile();
      }
    }

    super.onInit();
  }
}
