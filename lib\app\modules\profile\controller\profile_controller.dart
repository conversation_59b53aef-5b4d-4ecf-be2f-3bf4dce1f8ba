import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/profile/models/owner_profile_model.dart';
import 'package:ma3ak/app/modules/profile/models/user_profile_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';
import 'package:ma3ak/main.dart';

/// Controller for managing user and owner profile data and operations
/// Handles profile fetching, loading states, and user type determination
class ProfileController extends GetxController {
  // Loading state indicators for different profile types
  final isLoading = false.obs;           // User profile loading state
  final isLoadingOwner = false.obs;      // Owner profile loading state

  // User type determination and profile data containers
  RxBool isOwner = box.read('isOwner') == 1 ? true.obs : false.obs;  // User type flag from storage
  final userProfileData = UserProfileModel().obs;                   // Regular user profile data
  final ownerProfileData = OwnerProfileModel().obs;                 // Business owner profile data

  /// Fetches user profile data from API for regular users
  /// Loads profile information and updates UI state
  Future<void> getUserProfile() async {
    // Debug logging for authentication state
    debugPrint("token  ${box.read("token")}");
    debugPrint("isOwner  ${box.read("isOwner").toString()}");

    // Set loading state during API call
    isLoading(true);
    final data = await RemoteServicesImpl().getProfileUser();
    isLoading(false);

    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString()); // Log error if profile fetch fails
    }, (dataModel) {
      // Store fetched profile data and trigger UI update
      userProfileData.value = dataModel;
      update();
      debugPrint(userProfileData.value.user?.email);
    });
  }

  /// Fetches owner profile data from API for business owners
  /// Loads business owner profile information and updates UI state
  Future<void> getOwnerProfile() async {
    // Debug logging for authentication state
    debugPrint("token  ${box.read("token")}");
    debugPrint("isOwner  ${box.read("isOwner").toString()}");

    // Set loading state and trigger UI update
    isLoadingOwner(true);
    update();

    // Call API to fetch owner profile with authentication token
    final data = await RemoteServicesImpl().getProfileOwner(box.read('token'));
    isLoadingOwner(false);

    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString()); // Log error if profile fetch fails
    }, (dataModel) {
      // Store fetched owner profile data and trigger UI update
      ownerProfileData.value = dataModel;
      update();
      debugPrint(userProfileData.value.user?.email);
    });
  }

  /// Initialize controller by fetching appropriate profile based on user type
  /// Automatically loads profile data when controller is created
  @override
  void onInit() async {
    // Check if user is authenticated before fetching profile
    if (box.read('token') != '') {
      // Fetch profile based on user type (owner vs regular user)
      if (box.read('isOwner') == 1) {
        await getOwnerProfile();  // Load business owner profile
      } else {
        await getUserProfile();   // Load regular user profile
      }
    }

    super.onInit();
  }
}
