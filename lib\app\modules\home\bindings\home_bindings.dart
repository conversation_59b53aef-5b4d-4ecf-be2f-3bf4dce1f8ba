import 'package:get/get.dart';
import 'package:ma3ak/app/modules/favorite/controller/favorite_controller.dart';
import 'package:ma3ak/app/modules/home/<USER>/ads_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/place/controller/comments_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';

// Dependency injection bindings for home module controllers
class HomeBindings extends Bindings {
  // Registers controllers with GetX dependency injection
  @override
  void dependencies() {
    Get.lazyPut(() => AdsController());
    Get.lazyPut(() => PlaceController());
    Get.lazyPut(() => OffersController());
    Get.lazyPut(() => FavoriteController());
    Get.lazyPut(() => CommentsController());
  }
}
