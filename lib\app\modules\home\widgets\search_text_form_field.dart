import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ma3ak/config/theme/app_color.dart';

class SearchTextFormField extends StatelessWidget {
  const SearchTextFormField({
    super.key,
    this.searchController,
    this.readOnly = false, this.onTap, this.onChange,
  });
  final TextEditingController? searchController;
  final bool? readOnly;
  final Function()? onTap;
  final void Function(String)? onChange;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 16,
      ),
      child: TextFormField(
        onChanged:onChange ,
        controller: searchController,
        readOnly: readOnly!,
        onTap: onTap,
        decoration: InputDecoration(
          // filled: true,
          // fillColor: AppColor.borderColor,
          suffixIcon: IconButton(
            onPressed: () {},
            icon: const Icon(Icons.search),
          ),
          hintText: 'اكتب هنا...',
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: AppColor.primaryColor),
            borderRadius: BorderRadius.circular(30.r),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: AppColor.primaryColor),
            borderRadius: BorderRadius.circular(30.r),
          ),
          border: OutlineInputBorder(
            borderSide: const BorderSide(color: AppColor.primaryColor),
            borderRadius: BorderRadius.circular(30.r),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 0.h),
        ),
      ),
    );
  }
}
