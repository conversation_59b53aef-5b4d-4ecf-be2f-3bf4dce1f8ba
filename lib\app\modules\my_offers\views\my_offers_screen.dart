import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/my_offers/controller/delete_offer_controller.dart';
import 'package:ma3ak/app/modules/my_offers/controller/my_offers_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/offers/views/add_offer_screen.dart';
import 'package:ma3ak/app/modules/offers/widgets/card_offer.dart';
import 'package:ma3ak/app/modules/offers/widgets/search_text_field_offers.dart';
import 'package:ma3ak/app/modules/place/views/place_screen_owner.dart';
import 'package:ma3ak/app/modules/place/views/place_screen.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/widgets/main_app_bar.dart';

class MyOffersScreen extends StatelessWidget {
  MyOffersScreen({super.key});

  final MyOffersController myOffersController = Get.put(MyOffersController());
  final DeleteOfferController deleteOfferController =
      Get.put(DeleteOfferController());

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        myOffersController.fetchMyOffersOffers();
      },
      child: SafeArea(
        child: Column(
          children: [
            MainAppBar(
              showAction: true,
              action: box.read('isOwner') == 0 || box.read('isOwner') == null
                  ? Container()
                  : TextButton.icon(
                      onPressed: () {
                        Get.lazyPut(() => OffersController());
                        Get.to(() => AddOfferScreen());
                      },
                      label:
                          Text('اضافة عرض', style: TextStyle(fontSize: 16.sp)),
                      icon: const Icon(
                        Icons.add,
                      )),
            ),
            SearchTextFieldOffers(
              searchController: myOffersController.searchController,
              onChanged: (text) {
                myOffersController.searchOffers(text);
              },
            ),
            Obx(() {
              if (myOffersController.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              if (myOffersController.searchResult!.isEmpty) {
                return const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Center(
                      child: Text(
                    'لا توجد عروض',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  )),
                );
              } else {
                return Expanded(
                  child: ListView.separated(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        var offer = myOffersController.searchResult?[index];
                        return InkWell(
                            onTap: () async {
                              Get.defaultDialog(
                                barrierDismissible:
                                    false, // Prevent dismissing the dialog
                                title: 'جاري التحميل...',
                                content: const Center(
                                    child: CircularProgressIndicator()),
                              );
                              final OffersController offersController =
                                  Get.find<OffersController>();
                              var res = await offersController.getPlaceByOffer(
                                slugOffer: offer.slug,
                              );
                              res.fold((error) {
                                // Dismiss the loading dialog after data is fetched
                                Get.back();
                                debugPrint(error.toString());
                              }, (dataModel) async {
                                offersController.placeInfromationOld.value =
                                    dataModel;
                                await Get.find<OffersController>()
                                    .fetchOffersByPlace(
                                  slugPlace: offersController
                                      .placeInfromationOld.value.data!.slug!,
                                );
                                // Dismiss the loading dialog after data is fetched
                                Get.back();
                                if (box.read('isOwner') == 1) {
                                  Get.to(() => PlaceScreenOwner(
                                        placeInfromation: offersController
                                            .placeInfromationOld,
                                      ));
                                } else {
                                  Get.to(() => PlaceScreen(
                                      placeInfromation: offersController
                                          .placeInfromationOld));
                                }
                              });
                            },
                            child: CardOffer(offer: offer!));
                      },
                      separatorBuilder: (context, index) {
                        return const Divider();
                      },
                      itemCount: myOffersController.searchResult?.length ?? 0),
                );
              }
            })
          ],
        ),
      ),
    );
  }
}
