import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/favorite/controller/favorite_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/place/controller/greater_rate_place_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/place/views/place_screen_owner.dart';
import 'package:ma3ak/app/modules/place/views/place_screen.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/widgets/card_widget.dart';

/// Widget displaying a list of highest-rated places on the home screen
/// Shows up to 5 places with navigation to detailed place screens
class ListViewPlacesInHome extends StatelessWidget {
  const ListViewPlacesInHome({
    super.key,
    this.isNeverScrollable = true,
  });
  
  // Controls whether the list is scrollable or not
  final bool? isNeverScrollable;

  @override
  Widget build(BuildContext context) {
    // Controllers for managing place data and user profile
    final greaterRatePlaceController = Get.find<GreaterRatePlaceController>();
    Get.put(ProfileController());

    return Column(
      children: [
        // Section header with title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Row(
              children: [
                Text(
                  'الأماكن الأكثر تقييماً', // "Most Rated Places"
                  style:
                      TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ),
        // List of place cards with conditional scrolling
        ListView.separated(
          shrinkWrap: true,
          physics: isNeverScrollable!
              ? const NeverScrollableScrollPhysics()
              : const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemBuilder: (context, index) {
            // Get place data from controller
            final placeCards = greaterRatePlaceController.greaterRatePlaceData.value.data;
            final placeCard = placeCards?[index];
            var owner =
                Get.find<ProfileController>().ownerProfileData.value.owner;

            return InkWell(
              // Handle place card tap - navigate to place details
              onTap: () async {
                // Show loading dialog while fetching place details
                Get.dialog(
                  Center(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        color: AppColor.primaryColor
                      ),
                      height: 100.h,
                      width: 100.w,
                      child: const Center(child: CircularProgressIndicator(color: Colors.white,),),
                    ),
                  ),
                  barrierColor: Colors.transparent,
                );

                final placeController = Get.find<PlaceController>();
                // Fetch detailed place information
                var res = await placeController.getPlaceInforamtion(
                    slugPlace: placeCard.slug);

                res.fold((error) {
                  debugPrint(error.toString());
                  Get.back(); // Dismiss loading dialog on error
                }, (dataModel) async {
                  // Fetch offers for this place
                  await Get.find<OffersController>()
                      .fetchOffersByPlace(slugPlace: placeCard.slug);
                  placeController.placeInfromationOld.value = dataModel;
                  Get.back(); // Dismiss loading dialog
                  
                  // Navigate to appropriate screen based on user type
                  if (box.read('isOwner') == 1) {
                    Get.to(() => PlaceScreenOwner(
                          placeInfromation: placeController.placeInfromationOld,
                        ));
                  } else {
                    Get.to(() => PlaceScreen(
                        placeInfromation: placeController.placeInfromationOld));
                  }
                });
              },
              // Place card widget with rating, image, and action buttons
              child: CardWidget(
                rating: placeCard!.stars.toDouble(),
                title: placeCard.name,
                imgUrl: placeCard.coverImage,
                isFave: null,
                // Show edit button only for place owners
                showIsEdit: box.read('isOwner') == 1
                    ? placeCard.ownerId == owner?.id
                    : null,
                // Handle edit button press - navigate to update place screen
                onPressedEdit: () {
                  var owner = Get.find<ProfileController>()
                      .ownerProfileData
                      .value
                      .owner;

                  if (placeCard.ownerId == owner?.id) {
                    Get.toNamed(Routes.updatePlace, arguments: placeCard);
                  }
                },
                // Handle favorite button press - add/remove from favorites
                onPressed: () {
                  if (box.read('token') != null && box.read('isOwner') != 1) {
                    Get.find<FavoriteController>()
                        .addOrRemoveFavorite(slugPlace: placeCard.slug);
                  } else {
                    // Show login required message
                    Get.snackbar('الرسالة', 'يجب تسجيل الدخول اولا',
                        backgroundColor: Colors.green,
                        colorText: Colors.white,
                        icon: const Icon(Icons.warning, color: Colors.white));
                  }
                },
                governor: placeCard.governor,
                directorate: placeCard.directorate,
              ),
            );
          },
          // Empty separator between list items
          separatorBuilder: (context, index) {
            return const SizedBox();
          },
          // Limit to maximum 5 items or total available items
          itemCount: (greaterRatePlaceController.greaterRatePlaceData.value.data?.length ?? 0) > 5
              ? 5
              : (greaterRatePlaceController.greaterRatePlaceData.value.data?.length ?? 0),
        ),
      ],
    );
  }
}
