import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/offers/models/offer_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class MyOffersController extends GetxController {
  var isLoading = false.obs;
  final offerData = OfferModel().obs;

  TextEditingController searchController = TextEditingController();
  RxList<OfferDatum>? searchResult = <OfferDatum>[].obs;

  Future<void> fetchMyOffersOffers() async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .fetchMyOffersOffers(); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      offerData.value = dataModel;
      searchResult?.value = dataModel.data!;
      debugPrint(dataModel.toString());
    });
  }

  void searchOffers(String text) {
    if (text.isEmpty) {
      searchResult?.value = offerData.value.data!;
    } else {
      searchResult?.value = offerData.value.data!
          .where(
            (element) =>
                element.title.toLowerCase().contains(text.toLowerCase()),
          )
          .toList();
    }
  }

  @override
  void onInit() async {
    await fetchMyOffersOffers(); // Fetch ads on initialization
    super.onInit();
  }
}
