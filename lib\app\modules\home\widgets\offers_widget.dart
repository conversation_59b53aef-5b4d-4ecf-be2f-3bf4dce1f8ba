import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/navbar/views/nav_bar_view.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/place/views/place_screen.dart';
import 'package:ma3ak/app/modules/place/views/place_screen_owner.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/main.dart';

class OffersWidget extends StatelessWidget {
  OffersWidget({
    super.key,
  });
  final OffersController offersController =
      Get.put<OffersController>(OffersController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('العروض'),
              TextButton.icon(
                iconAlignment: IconAlignment.end,
                onPressed: () {
                  Get.find<NavbarController>().selectedIndex.value = 1;
                  Get.to(() => const NavBarView());
                },
                label: const Text(
                  'المزيد',
                  style: TextStyle(
                    color: AppColor.blueColor1,
                  ),
                ),
                icon: Icon(
                  Icons.arrow_forward_ios,
                  size: 12.sp,
                  color: AppColor.blueColor1,
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 100.h,
          child: ListView.separated(
            padding: EdgeInsets.only(right: 16.w),
            scrollDirection: Axis.horizontal,
            itemBuilder: (BuildContext context, int index) {
              var offerItem = offersController.offerData.value.data![index];
              return InkWell(
                onTap: () async {
                   Get.dialog(
  Center(
    child: Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
      color: AppColor.primaryColor
      ),
      height: 100.h, // Adjust height to your desired value
      width: 100.w,   // Adjust width to your desired value
      child: const Center(child: CircularProgressIndicator(color: Colors.white,),),
    ),
  ),
  barrierColor: Colors.transparent,
);
                  var res = await offersController.getPlaceByOffer(
                    slugOffer: offerItem.slug,
                  );
                  res.fold((error) {
                    // Dismiss the loading dialog after data is fetched
                    Get.back();
                    debugPrint(error.toString());
                  }, (dataModel) async {
                    offersController.placeInfromationOld.value = dataModel;
                    await Get.find<OffersController>().fetchOffersByPlace(
                        slugPlace: offersController
                            .placeInfromationOld.value.data!.slug!);
                    // Dismiss the loading dialog after data is fetched
                    Get.back();
                    if (box.read('isOwner') == 1) {
                      Get.to(() => PlaceScreenOwner(
                            placeInfromation:
                                offersController.placeInfromationOld,
                          ));
                    } else {
                      Get.to(() => PlaceScreen(
                          placeInfromation:
                              offersController.placeInfromationOld));
                    }
                  });
                },
                child: Container(
                  height: 100.h,
                  width: 100.w,
                  decoration: BoxDecoration(
                    // color: Colors.amber,
                    borderRadius: BorderRadius.circular(4.r),
                    border: Border.all(color: Colors.grey.withOpacity(.5)),
                  ),
                  child: CachedNetworkImage(
                      fit: BoxFit.contain,
                      progressIndicatorBuilder: (context, url, progress) =>
                          const Center(
                            child: CircularProgressIndicator(),
                          ),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error),
                      imageUrl: ApiList.baseUrl + offerItem.img),
                ),
              );
            },
            separatorBuilder: (BuildContext context, int index) {
              return Padding(padding: EdgeInsets.only(left: 10.w));
            },
            itemCount: offersController.offerData.value.data?.length ?? 0,
          ),
        ),
      ],
    );
  }
}
