import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/controller/auth_controller.dart';
import 'package:ma3ak/app/modules/auth/controller/login_controller.dart';
import 'package:ma3ak/app/modules/auth/views/register_screen.dart';
import 'package:ma3ak/app/modules/auth/widgets/text_form_field_auth.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/utils/images_pic.dart';
import 'package:ma3ak/widgets/reactive_button.dart';
import 'package:ma3ak/widgets/widgets/basic_app_bar.dart';

// User login screen with form validation and authentication
class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  // Builds the login screen with form and authentication logic
  @override
  Widget build(BuildContext context) {
    // Initialize controllers for login functionality
    final loginController = Get.put(LoginController());
    final authController = Get.put(AuthController());
    var wdth = MediaQuery.of(context).size.width;
    var hgt = MediaQuery.of(context).size.height;

    return Scaffold(
        backgroundColor: AppColor.grayColor,
        appBar: const BasicAppbar(
          title: Text('تسجيل الدخول'),
        ),
        body: SingleChildScrollView(
            child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            // Main login form container
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              width: double.infinity,
              height: hgt * .75,
              decoration: BoxDecoration(
                color: AppColor.whiteColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Form(
                key: loginController.formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Image.asset(
                      ImagesPic.logoM,
                      height: 100,
                      width: 100,
                    ),
                    const Text(
                      'أهلاً وسهلاً بك في معاك ',
                      // style: TextStyle(color: Colors.white),
                    ),
                    Column(
                      children: [
                        TextFormFieldAuth(
                          controller: authController.emailController,
                          labelText: 'البريد الإلكتروني',
                          prefixIcon: const Icon(Icons.person),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'الرجاء إدخال البريد الإلكتروني';
                            }
                            if (!value.endsWith('@gmail.com')) {
                              return 'البريد الإلكتروني يجب أن ينتهي بـ @gmail.com';
                            }
                            return null; // No errors
                          },
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        Obx(
                          () => TextFormFieldAuth(
                            controller: authController.passwordController,
                            labelText: 'كلمة المرور',
                            prefixIcon: const Icon(Icons.person),
                            obscureText:
                                !loginController.isPasswordVisible.value,
                            suffixIcon: IconButton(
                              icon: Icon(
                                loginController.isPasswordVisible.value
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                              ),
                              onPressed:
                                  loginController.togglePasswordVisibility,
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء إدخال كلمة المرور';
                              }
                              if (value.length < 6) {
                                return 'كلمة المرور يجب أن تكون 6 أحرف أو أكثر';
                              }
                              return null; // No errors
                            },
                          ),
                        ),
                      ],
                    ),
                    Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Obx(
                                () => Checkbox(
                                  value:
                                      loginController.isRememberMeChecked.value,
                                  onChanged: (value) {
                                    loginController.toggleRememberMe();
                                  },
                                ),
                              ),
                              const Text('تذكرني'),
                            ],
                          ),
                          TextButton(
                            onPressed: () {},
                            child: const Text('نسيت كلمة السر ؟'),
                          )
                        ]),
                    Builder(builder: (context) {
                      return ReactiveButton(
                        wdth: wdth,
                        text: 'تسجيل',
                        onPressed: () async {
                          if (loginController.formKey.currentState!
                              .validate()) {
                            final reactiveButtonController =
                                Get.find<ReactiveButtonController>();
                            reactiveButtonController.isLoading.value = true;
                            await authController.loginUser(
                              authController.emailController.text,
                              authController.passwordController.text,
                            );
                            reactiveButtonController.isLoading.value = false;
                          }
                        },
                      );
                    }),
                    Column(
                      children: [
                        TextButton(
                          onPressed: () {
                            Get.off(const RegisterScreen());
                          },
                          child: const Text(
                            'انشاء حساب جديد !',
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
        )));
  }
}
