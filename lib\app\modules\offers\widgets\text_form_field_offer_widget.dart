import 'package:flutter/material.dart';

class TextFormFieldOfferWidget extends StatelessWidget {
  const TextFormFieldOfferWidget({
    super.key,
    required this.labelText,
    required this.hintText,
    this.controller, this.validator,
  });
  final String labelText;
  final String hintText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: TextFormField(
        validator: validator,
        controller: controller,
        decoration: InputDecoration(
          labelText: labelText,
          contentPadding: const EdgeInsets.only(right: 10),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          hintText: hintText,
        ),
      ),
    );
  }
}
