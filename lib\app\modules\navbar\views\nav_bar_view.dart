import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/views/login_screen.dart';
import 'package:ma3ak/app/modules/dashboard/views/dashboard_screen.dart';
import 'package:ma3ak/app/modules/favorite/views/favorite_screen.dart';
import 'package:ma3ak/app/modules/home/<USER>/home_screen.dart';
import 'package:ma3ak/app/modules/my_offers/controller/my_offers_controller.dart';
import 'package:ma3ak/app/modules/my_offers/views/my_offers_screen.dart';
import 'package:ma3ak/app/modules/my_place/views/my_place_screen.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/navbar/widgets/bottom_navbar.dar.dart';
import 'package:ma3ak/app/modules/navbar/widgets/drawer_widget.dart';
import 'package:ma3ak/app/modules/offers/views/offers_screen.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/app/modules/profile/views/profile_screen.dart';
import 'package:ma3ak/app/modules/profile/views/profile_owner_screen.dart';
import 'package:ma3ak/app/modules/search/views/search_screen.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/utils/svg_icon.dart';

// Main navigation view with bottom navigation bar and screen management
class NavBarView extends StatelessWidget {
  const NavBarView({super.key});
  
  // Builds the main navigation structure with different screens
  @override
  Widget build(BuildContext context) {
    // Initialize scaffold key for potential messenger functionality
    GlobalKey<ScaffoldMessengerState> scaffoldKey = GlobalKey();
    scaffoldKey;
    
    // Define screens based on user type (owner vs regular user)
    List<Widget> screens = [
      box.read('isOwner') == 1 ? ProfileOwnerScreen() : const ProfileScreen(),
      box.read('isOwner') == 1 ? MyOffersScreen() : OffersScreen(),
      box.read('isOwner') == 1 ? DashboardScreen() : const HomeScreen(),
      SearchScreen(),
      box.read('token') != null
          ? box.read('isOwner') == 1
              ? const MyPlaceScreen()
              : FavoriteScreen()
          : const LoginScreen()
    ];
    
    // Build navigation with controller state management
    return GetBuilder<NavbarController>(builder: (navController) {
      return PopScope(
        canPop: navController.canPop.value,
        // Handle back button press with exit confirmation dialog
        onPopInvokedWithResult: (didPop, result) {
          // Show exit confirmation dialog in Arabic
          Get.dialog(AlertDialog(
            backgroundColor: Colors.white,
            title: const Text('الخروج من التطبيق'),
            content: const Text('هل تريد الخروج من التطبيق ؟'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('لا'),
              ),
              TextButton(
                onPressed: () {
                  navController.canPop.value = true;
                  SystemNavigator.pop();
                },
                child: const Text('نعم'),
              ),
            ],
          ));
        },
        child: AnnotatedRegion<SystemUiOverlayStyle>(
            // Configure system UI overlay styling (status bar, navigation bar)
            value: const SystemUiOverlayStyle(
              systemNavigationBarColor: Colors.white,
              systemNavigationBarIconBrightness: Brightness.dark,
              statusBarIconBrightness: Brightness.dark,
              statusBarColor: Colors.transparent,
              statusBarBrightness: Brightness.dark,
            ),
            child: Scaffold(
              backgroundColor: AppColor.primaryBack,
              resizeToAvoidBottomInset: false,
              extendBodyBehindAppBar: true,
              // key: scaffoldKey,
              extendBody: true,
              drawer: const DrawerWidget(),
              // Central floating action button for home navigation
              floatingActionButton: Container(
                // Add shadow decoration to floating action button
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(50.r)),
                  boxShadow: [
                    BoxShadow(
                      color: AppColor.primaryColor.withOpacity(0.3),
                      offset: const Offset(
                        0.0,
                        5.0,
                      ),
                      blurRadius: 10.r,
                      spreadRadius: 2.r,
                    ),
                  ],
                ),
                child: InkWell(
                  borderRadius: BorderRadius.circular(52.r),
                  // Navigate to home screen (index 2) when tapped
                  onTap: () {
                    navController.selectedIndex(2);
                  },
                  child: Container(
                    height: 65.r, //56.r,
                    width: 65.r,
                    // Style the floating action button container
                    decoration: BoxDecoration(
                        color: AppColor.primaryColor,
                        borderRadius: BorderRadius.circular(56.r),
                        boxShadow: [
                          BoxShadow(
                              color: AppColor.primaryColor.withOpacity(0.34),
                              blurRadius: 10.r,
                              offset: const Offset(0, 6))
                        ]),
                    child: SizedBox(
                      height: 60.h,
                      width: 60.w,
                      // Reactive home icon that changes color based on selection
                      child: Obx(
                        () => CircleAvatar(
                            backgroundColor: AppColor.primaryColor,
                            child: SvgPicture.asset(
                              SvgIcon.home,
                              height: 30.h,
                              width: 30.w,
                              colorFilter: ColorFilter.mode(
                                  navController.selectedIndex.value == 2
                                      ? AppColor.whiteColor
                                      : AppColor.grayColor,
                                  BlendMode.srcIn),
                            )),
                      ),
                    ),
                  ),
                ),
              ),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.centerDocked,
               // Reactive bottom navigation bar with custom items
               bottomNavigationBar: Obx(() {
                return BottomAppBar(
                  color: Colors.white,
                  elevation: 5,
                  notchMargin: 5,
                  clipBehavior: Clip.antiAlias,
                  shape: const CircularNotchedRectangle(),
                  child: Row(children: [
                    // Profile/Account navigation item
                    BottomNavItem(
                        tittle: "الحساب".tr,
                        imageData: navController.selectedIndex.value == 0
                            ? SvgIcon.profileActive
                            : SvgIcon.profile,
                        isSelected: navController.selectedIndex.value == 0,
                        onTap: () async {
                          // Check authentication and load appropriate profile
                          if (box.read('token') != null) {
                            Get.lazyPut(() => ProfileController());
                            if (box.read('isOwner') == 1) {
                              await Get.find<ProfileController>()
                                  .getOwnerProfile();
                            } else {
                              await Get.find<ProfileController>()
                                  .getUserProfile();
                            }

                            navController.selectPage(0);
                          } else {
                            Get.to(() => const LoginScreen());
                          }
                        }),
                    // Offers navigation item (My Offers for owners, All Offers for users)
                    BottomNavItem(
                        tittle:
                            box.read('isOwner') == 1 ? "عروضي".tr : "العروض".tr,
                        imageData: navController.selectedIndex.value == 1
                            ? SvgIcon.ads
                            : SvgIcon.ads,
                        isSelected: navController.selectedIndex.value == 1,
                        onTap: () {
                          // Load offers data for owners before navigation
                          if (box.read('isOwner') == 1) {
                            Get.find<MyOffersController>()
                                .fetchMyOffersOffers();
                            navController.selectPage(1);
                          }
                          navController.selectPage(1);
                        }),
                    // Spacer for floating action button
                    const Expanded(
                      child: SizedBox(),
                    ),
                    // Search navigation item
                    BottomNavItem(
                        tittle: "البحث".tr,
                        imageData: navController.selectedIndex.value == 3
                            ? SvgIcon.search
                            : SvgIcon.search,
                        isSelected: navController.selectedIndex.value == 3,
                        onTap: () => navController.selectPage(3)),
                    // Places/Favorites navigation item (My Places for owners, Favorites for users)
                    BottomNavItem(
                        tittle: box.read('isOwner') == 1
                            ? "أماكني".tr
                            : "المفضلة".tr,
                        imageData: navController.selectedIndex.value == 4
                            ? box.read('isOwner') == 1
                                ? SvgIcon.places
                                : SvgIcon.favActive
                            : box.read('isOwner') == 1
                                ? SvgIcon.places
                                : SvgIcon.fav,
                        isSelected: navController.selectedIndex.value == 4,
                        onTap: () async {
                          // Check authentication before accessing favorites/places
                          if (box.read('token') != null) {
                            navController.selectPage(4);
                          } else {
                            Get.to(() => const LoginScreen());
                          }
                        }),
                  ]),
                );
              }),
              // Display selected screen based on navigation index
              body: Obx(() {
                return screens[navController.selectedIndex.value];
              }),
            )),
      );
    });
  }
}
