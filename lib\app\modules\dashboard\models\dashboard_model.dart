/// Model for dashboard API response containing owner statistics
class DashboardModel {
  // Response status indicating success/failure
  bool? status;
  
  // Response message with additional information
  String? message;
  
  // Dashboard data containing statistics
  Data? data;

  DashboardModel({this.status, this.message, this.data});

  // Creates DashboardModel from JSON response
  DashboardModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ?  Data.fromJson(json['data']) : null;
  }

  // Converts DashboardModel to JSON format
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

/// Data model containing dashboard statistics for business owners
class Data {
  // Total number of offers created by owner
  int? offers;
  
  // Total number of places owned
  int? places;
  
  // Total number of visitors to owner's places
  int? visitorPlace;

  Data({this.offers, this.places, this.visitorPlace});

  // Creates Data from JSON response
  Data.fromJson(Map<String, dynamic> json) {
    offers = json['Offers'];
    places = json['Places'];
    visitorPlace = json['visitor_place'];
  }

  // Converts Data to JSON format
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Offers'] = offers;
    data['Places'] = places;
    data['visitor_place'] = visitorPlace;
    return data;
  }
}
