/// Model representing API response for comment submission
class SendCommentModel {
  // Response status indicating success/failure
  final bool? status;

  // Response message with additional information
  final String? message;

  // Comment data returned after successful submission
  final CommentData? data;

  SendCommentModel({
     this.status,
     this.message,
     this.data,
  });

  // Creates SendCommentModel from JSON response
  factory SendCommentModel.fromJson(Map<String, dynamic> json) {
    return SendCommentModel(
      status: json['status'],
      message: json['message'],
      data: CommentData.fromJson(json['data']),
    );
  }

  // Converts SendCommentModel to JSON format
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson()??[],
    };
  }
}

/// Model representing comment data after submission
class CommentData {
  // ID of the user who submitted the comment
  final int userId;

  // ID of the place the comment was made on
  final int placeId;

  // Comment text content
  final String comment;

  // Comment last update timestamp
  final String updatedAt;

  // Comment creation timestamp
  final String createdAt;

  // Unique comment identifier
  final int id;

  CommentData({
    required this.userId,
    required this.placeId,
    required this.comment,
    required this.updatedAt,
    required this.createdAt,
    required this.id,
  });

  // Creates CommentData from JSON response
  factory CommentData.fromJson(Map<String, dynamic> json) {
    return CommentData(
      userId: json['user_id'],
      placeId: json['place_id'],
      comment: json['comment'],
      updatedAt: json['updated_at'],
      createdAt: json['created_at'],
      id: json['id'],
    );
  }

  // Converts CommentData to JSON format
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'place_id': placeId,
      'comment': comment,
      'updated_at': updatedAt,
      'created_at': createdAt,
      'id': id,
    };
  }
}
