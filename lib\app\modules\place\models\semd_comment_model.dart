class SendCommentModel {
  final bool? status;
  final String? message;
  final CommentData? data;

  SendCommentModel({
     this.status,
     this.message,
     this.data,
  });

  // Factory method to create a SendCommentModel from JSON
  factory SendCommentModel.fromJson(Map<String, dynamic> json) {
    return SendCommentModel(
      status: json['status'],
      message: json['message'],
      data: CommentData.fromJson(json['data']),
    );
  }

  // Method to convert SendCommentModel into JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson()??[],
    };
  }
}

class CommentData {
  final int userId;
  final int placeId;
  final String comment;
  final String updatedAt;
  final String createdAt;
  final int id;

  CommentData({
    required this.userId,
    required this.placeId,
    required this.comment,
    required this.updatedAt,
    required this.createdAt,
    required this.id,
  });

  // Factory method to create CommentData from JSON
  factory CommentData.from<PERSON><PERSON>(Map<String, dynamic> json) {
    return CommentData(
      userId: json['user_id'],
      placeId: json['place_id'],
      comment: json['comment'],
      updatedAt: json['updated_at'],
      createdAt: json['created_at'],
      id: json['id'],
    );
  }

  // Method to convert CommentData into JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'place_id': placeId,
      'comment': comment,
      'updated_at': updatedAt,
      'created_at': createdAt,
      'id': id,
    };
  }
}
