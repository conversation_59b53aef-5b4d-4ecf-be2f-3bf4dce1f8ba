import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/config/routes/app_pages.dart';

// Root application widget that configures the entire app
class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // Builds the main app structure with responsive design and routing
  @override
  Widget build(BuildContext context) {
    // Initialize responsive design with base screen dimensions
    return ScreenUtilInit(
      designSize: const Size(360, 800),
      
      // Configure GetX material app with Arabic locale and routing
      builder: ((context, child) => GetMaterialApp(
            locale: const Locale('ar'),
            title: 'Flutter Demo',
            debugShowCheckedModeBanner: false,
            // theme: ThemeData(
            //     appBarTheme: AppBarTheme(
            //       // color: Colors.white,
            //       backgroundColor: Color.fromARGB(255, 0, 0, 0),
            //       elevation: 0,
            //     ),
            //     useMaterial3: true),
            // initialBinding: NavbarBinding(),
            initialRoute: AppPages.initial,
            getPages: AppPages.pages,
            // home: const NavBarView(),
          )),
    );
  }
}
