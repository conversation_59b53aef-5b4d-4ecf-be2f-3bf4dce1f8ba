import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DescriptionDetailsWidgetTest extends StatelessWidget {
  const DescriptionDetailsWidgetTest({
    super.key, required this.des,
  });
final String des;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الوصف',
            style:
                TextStyle(fontWeight: FontWeight.bold, fontSize: 18.sp),
          ),
          Text(des, style: TextStyle(
              height:
                  2.0.sp, // Adjust the height value for line spacing
            ),
          ),
        ],
      ),
    );
  }
}
