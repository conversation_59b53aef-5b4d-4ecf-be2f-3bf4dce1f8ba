import 'package:get/get.dart';
import 'package:ma3ak/app/modules/home/<USER>/home_bindings.dart';
import 'package:ma3ak/app/modules/navbar/bindings/navbar_binding.dart';
import 'package:ma3ak/app/modules/navbar/views/nav_bar_view.dart';
import 'package:ma3ak/app/modules/splash/binding/splash_binding.dart';
import 'package:ma3ak/app/modules/splash/views/splash_screen.dart';
import 'package:ma3ak/app/modules/update_place/views/update_place_screen.dart';
import 'package:ma3ak/config/routes/app_routes.dart';

// App pages configuration with GetX routing and bindings
class AppPages {
  AppPages._();

  // Initial route when app starts
  static String initial = "/splash/";

  // List of all app pages with their routes and bindings
  static final pages = [
    // Splash screen - app entry point
    GetPage(
      name: initial,
      page: () => const SplashScreen(),
      binding: SplashBinding(),
    ),
    
    // Main navigation with bottom navbar
    GetPage(
      name: Routes.navBarView,
      page: () =>  const NavBarView(),
      bindings: [
        HomeBindings(),
        NavbarBinding(),
      ],
    ),
    
    // Place update screen for owners
    GetPage(
      name: Routes.updatePlace,
      page: () =>  UpdatePlaceScreen(),
      bindings: [
        HomeBindings(),
      ],
    ),
    
    // GetPage(
    //   name: Routes.home,
    //   page: () => const HomeScreen(),
    //   binding: HomeBindings(),
    // ),
    // GetPage(
    //   name: Routes.language,
    //   page: () => const ChangeLanguageView(),
    //   binding: LanguageBindings(),
    // )
  ];
}
