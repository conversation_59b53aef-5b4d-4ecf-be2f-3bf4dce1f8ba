import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/home/<USER>/ads_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart'; // Ensure this contains the fetchAds method

// Controller for managing advertisements display and carousel functionality
class AdsController extends GetxController {
  // Loading state and data models for ads
  final isLoading = false.obs;
  final adData = AdModel().obs;
  final dotIndex = 0.obs;

  // Updates the current dot indicator for ads carousel
  void handleAdsDots(int index) {
    dotIndex.value = index;
  }

  // Fetches advertisements from API
  Future<void> fetchAds() async {
    isLoading(true);
    final data =
        await RemoteServicesImpl().fetchAds(); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      adData.value = dataModel;
      debugPrint(adData.value.toString());
    });
  }

  // Initializes controller by fetching ads data
  @override
  void onInit()  {
     fetchAds(); // Fetch ads on initialization
    super.onInit();
  }
}
