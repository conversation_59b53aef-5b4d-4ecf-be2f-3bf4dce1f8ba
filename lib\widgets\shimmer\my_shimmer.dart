import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Enum defining shimmer animation directions
enum ShimmerDirection { ltr, rtl, ttb, btt }

// Controller for managing shimmer animation state and lifecycle
class ShimmerController extends GetxController with GetTickerProviderStateMixin {
  late AnimationController controller;
  RxDouble percent = 0.0.obs; // Animation progress percentage
  final Duration period; // Animation duration
  final int loop; // Number of animation loops
  int _count = 0; // Current loop count

  // Initialize controller with animation settings and listeners
  ShimmerController({required this.period, required this.loop}) {
    controller = AnimationController(vsync: this, duration: period)
      ..addListener(() => percent.value = controller.value)
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _count++;
          if (loop <= 0) {
            controller.repeat();
          } else if (_count < loop) {
            controller.forward(from: 0.0);
          }
        }
      });
  }

  // Animation control methods
  void start() => controller.forward();
  void stop() => controller.stop();

  @override
  void onClose() {
    controller.dispose();
    super.onClose();
  }
}

// Main shimmer widget that applies shimmer effect to child widgets
class Shimmer extends StatelessWidget {
  final Widget child; // Widget to apply shimmer effect to
  final Duration period; // Animation duration
  final ShimmerDirection direction; // Animation direction
  final Gradient gradient; // Shimmer gradient colors
  final int loop; // Number of animation loops
  final bool enabled; // Enable/disable shimmer effect

  const Shimmer({
    super.key,
    required this.child,
    required this.gradient,
    this.direction = ShimmerDirection.ltr,
    this.period = const Duration(milliseconds: 1500),
    this.loop = 0,
    this.enabled = true,
  });

  // Builds the shimmer widget with controller and animation
  @override
  Widget build(BuildContext context) {
    final shimmerController = Get.put(ShimmerController(period: period, loop: loop));
    if (enabled) shimmerController.start();

    return Obx(() => _Shimmer(
          direction: direction,
          gradient: gradient,
          percent: shimmerController.percent.value,
          child: child,
        ));
  }
}

// Internal shimmer implementation widget that handles the actual shimmer effect
class _Shimmer extends StatelessWidget {
  final Widget child; // Child widget to apply effect to
  final double percent; // Animation progress (0.0 to 1.0)
  final ShimmerDirection direction; // Animation direction
  final Gradient gradient; // Shimmer gradient

  const _Shimmer({
    required this.child,
    required this.percent,
    required this.direction,
    required this.gradient,
  });

  // Builds the shimmer effect using ShaderMask with directional gradient
  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) {
        final width = bounds.width;
        final height = bounds.height;
        Rect rect;
        double dx, dy;

        // Calculate shimmer position based on direction
        if (direction == ShimmerDirection.rtl) {
          dx = _offset(width, -width, percent);
          dy = 0.0;
          rect = Rect.fromLTWH(dx - width, dy, 3 * width, height);
        } else if (direction == ShimmerDirection.ttb) {
          dx = 0.0;
          dy = _offset(-height, height, percent);
          rect = Rect.fromLTWH(dx, dy - height, width, 3 * height);
        } else if (direction == ShimmerDirection.btt) {
          dx = 0.0;
          dy = _offset(height, -height, percent);
          rect = Rect.fromLTWH(dx, dy - height, width, 3 * height);
        } else {
          dx = _offset(-width, width, percent);
          dy = 0.0;
          rect = Rect.fromLTWH(dx - width, dy, 3 * width, height);
        }

        return gradient.createShader(rect);
      },
      blendMode: BlendMode.srcIn,
      child: child,
    );
  }

  // Calculates offset position based on animation progress
  double _offset(double start, double end, double percent) {
    return start + (end - start) * percent;
  }
}
