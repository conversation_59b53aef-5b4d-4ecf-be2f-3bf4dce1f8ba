import 'package:get/get.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class LogoutAccountController extends GetxController {
  var isLoading = false.obs;

  Future<void> logoutUser() async {
    isLoading(true);
    // Call the logoutUser method from the authentication service
    var response = await RemoteServicesImpl().logoutUser();
    isLoading(false);
    response.fold((l) => Get.snackbar("Error", l), (r) async {
      Get.find<NavbarController>().selectedIndex.value = 2;
      Get.snackbar(
        "Success",
        r.message.toString(),
      );
      Get.find<ReactiveButtonController>().isLoading.value = false;
    });
    Get.find<ReactiveButtonController>().isLoading.value = false;
  }

  Future<void> logoutOwner() async {
    isLoading(true);
    var response = await RemoteServicesImpl().logoutOwner();
    isLoading(false);
    response.fold((l) => Get.snackbar("Error", l), (r) async{
      Get.offAllNamed(Routes.navBarView);
      Get.snackbar(
        "Success",
        r.message.toString(),
      );
      Get.find<ReactiveButtonController>().isLoading.value = false;
    });
    Get.find<ReactiveButtonController>().isLoading.value = false;
  }
}
