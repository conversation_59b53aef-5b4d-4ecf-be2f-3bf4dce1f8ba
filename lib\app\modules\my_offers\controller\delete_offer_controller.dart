import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/semd_comment_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class DeleteOfferController extends GetxController {
  var isLoading = false.obs;
  Rx<SendCommentModel>? sendCommentModel = SendCommentModel().obs;

  Future<void> deleteOffer({required String slugOffer}) async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .deleteOffer(slugOffer: slugOffer); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      // sendCommentModel?.value = dataModel;
      debugPrint(dataModel.toString());
    });
  }
}
