import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/semd_comment_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class SendCommentController extends GetxController {
  TextEditingController sendTextController = TextEditingController();
  var sendText = ''.obs;
  Rx<SendCommentModel>? sendCommentData = SendCommentModel().obs;
  final isLoading = false.obs;
  Future<void> sendComment({required String slugPlace}) async {
    final Map<String, dynamic> body = {
      'comment': sendTextController.text,
    };
    isLoading(true);
    final data = await RemoteServicesImpl().sendComment(
        slugPlace: slugPlace, body: body); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      sendCommentData?.value = dataModel;
      sendTextController.clear();
      debugPrint(dataModel.toString());
    });
  }
}
