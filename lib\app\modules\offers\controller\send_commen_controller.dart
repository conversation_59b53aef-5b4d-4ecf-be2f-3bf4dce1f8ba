import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/semd_comment_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for handling comment submission functionality
class SendCommentController extends GetxController {
  // Text input controller for comment text field
  TextEditingController sendTextController = TextEditingController();

  // Observable string for comment text (currently unused)
  var sendText = ''.obs;

  // Observable container for API response data
  Rx<SendCommentModel>? sendCommentData = SendCommentModel().obs;

  // Loading state indicator for comment submission
  final isLoading = false.obs;

  /// Sends a comment to a specific place using its slug identifier
  Future<void> sendComment({required String slugPlace}) async {
    // Prepare request body with comment text
    final Map<String, dynamic> body = {
      'comment': sendTextController.text,
    };

    isLoading(true);

    // Call API service to submit the comment
    final data = await RemoteServicesImpl().sendComment(
        slugPlace: slugPlace, body: body);
    isLoading(false);

    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      sendCommentData?.value = dataModel;
      sendTextController.clear(); // Clear input after successful submission
      debugPrint(dataModel.toString());
    });
  }
}
