import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/my_place/controller/my_place_controller.dart';
import 'package:ma3ak/app/modules/my_place/widgets/list_view_places_widget.dart';
import 'package:ma3ak/widgets/widgets/main_app_bar.dart';

class MyPlaceScreen extends StatelessWidget {
  const MyPlaceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const MainAppBar(),
        RefreshIndicator(
          onRefresh: () async {
            await Get.find<MyPlaceController>().fetchMyPlace();
          },
          child: ListViewMyPlacesWidget(
            isNeverScrollable: false,
          ),
        ),
      ],
    );
  }
}
