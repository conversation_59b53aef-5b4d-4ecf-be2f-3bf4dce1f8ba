import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class SendReplyController extends GetxController {
  TextEditingController sendTextController = TextEditingController();
  var sendText = ''.obs;
  // Rx<SendCommentModel>? sendCommentData = SendCommentModel().obs;
  final isLoading = false.obs;
  Future<void> sendReply({required int commentId}) async {
    final Map<String, dynamic> body = {
      'comment': sendTextController.text,
    };
    isLoading(true);
    final data = await RemoteServicesImpl().sendReplyComment(
        commentId: commentId, body: body); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      // sendCommentData?.value = dataModel;
      sendTextController.clear();
      debugPrint(dataModel.toString());
    });
  }

  var activeReplyIndex = Rx<int?>(null);

  void toggleReply(int index) {
    // If the same reply is clicked again, hide the TextField
    if (activeReplyIndex.value == index) {
      activeReplyIndex.value = null;
    } else {
      activeReplyIndex.value = index;
    }
  }
}
