import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for handling reply submission to comments
class SendReplyController extends GetxController {
  // Text input controller for reply text field
  TextEditingController sendTextController = TextEditingController();

  // Observable string for reply text (currently unused)
  var sendText = ''.obs;

  // Loading state indicator for reply submission
  final isLoading = false.obs;

  /// Sends a reply to a specific comment using its ID
  Future<void> sendReply({required int commentId}) async {
    // Prepare request body with reply text
    final Map<String, dynamic> body = {
      'comment': sendTextController.text,
    };

    isLoading(true);

    // Call API service to submit the reply
    final data = await RemoteServicesImpl().sendReplyComment(
        commentId: commentId, body: body);
    isLoading(false);

    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      sendTextController.clear(); // Clear input after successful submission
      debugPrint(dataModel.toString());
    });
  }

  // Index of currently active reply input field
  var activeReplyIndex = Rx<int?>(null);

  /// Toggles reply input field visibility for a specific comment
  void toggleReply(int index) {
    // If the same reply is clicked again, hide the TextField
    if (activeReplyIndex.value == index) {
      activeReplyIndex.value = null;
    } else {
      activeReplyIndex.value = index;
    }
  }
}
