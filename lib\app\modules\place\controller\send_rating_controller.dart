import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/rating_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class SendRatingController extends GetxController {
  var isLoading = false.obs;
  var rating = 0.0.obs;

  Future<Either<String, RatingModel>> userRating(
      {required String slugPlace, required double rating}) async {
    final Map<String, dynamic> body = {"stars_rated": rating.toDouble()};
    isLoading(true);
    final data = await RemoteServicesImpl().userRating(
        slugPlace: slugPlace, body: body); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      Get.snackbar('فشل', 'فشل التقييم');      debugPrint(error.toString());
    }, (dataModel) {
      if (dataModel.status == true) {
        Get.snackbar('الرسالة', 'تم التقييم بنجاح');
      } else {
        Get.snackbar('الرسالة', dataModel.message.toString());
      }
    });

    return data;
  }
}
