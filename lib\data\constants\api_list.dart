// Centralized API endpoint constants for consistent URL management
class ApiList {
  // Base URLs for API communication
  static const String baseUrl = "https://ma3ak-backend.novelsoft.com.co/";
  // "http://192.168.1.33:8000/"; //
  static const String apiBaseUrl = '${baseUrl}api/';

  // Authentication endpoints
  static const String registerUser = "${apiBaseUrl}register-user";
  static const String loginUser = "${apiBaseUrl}login";
  static const String loginOwner = "${apiBaseUrl}login";

  // Profile endpoints
  static const getPofileUser = "${apiBaseUrl}user/profile-user";
  static const getPofileOwner = "${apiBaseUrl}owner/profile-owner";

  // Places and content endpoints
  static const getPlaces = "${apiBaseUrl}get-places/";
  static const greaterRatePlace = "${apiBaseUrl}Greater-Rate-Place";
  static const getSections = "${apiBaseUrl}get-sections";
  static const getCategories = "${apiBaseUrl}get-categories/";
  static const ads = "${apiBaseUrl}ads";
  
  // Offers management endpoints
  static const createOffer = "${apiBaseUrl}owner/create-offer/";
  static const allOffers = "${apiBaseUrl}all-offers";
  static const getPlaceByOffer = "${apiBaseUrl}get-placeByOffer/";
  static const allOffersInPlace = "${apiBaseUrl}all-offers/";
  static const getOffersByType = "${apiBaseUrl}offers/";
  
  // User interaction endpoints
  static const sendComment = "${apiBaseUrl}user/send-comment/";
  static const getCommentsForPlace = "${apiBaseUrl}get-comments/";
  static const likeOffer = "${apiBaseUrl}user/like/";
  static const disLikeOffer = "${apiBaseUrl}user/dislike/";
  static const getFavorite = "${apiBaseUrl}user/get-favorite";
  static const addOrRemoveFavorite = "${apiBaseUrl}user/favorite/";
  
  // Profile update endpoints
  static const updateUserProfile = "${apiBaseUrl}user/update-user";
  static const updateOwnerProfile = "${apiBaseUrl}owner/update-owner";
  
  // Owner management endpoints
  static const myOffers = "${apiBaseUrl}owner/offers/";
  static const deleteOffer = "${apiBaseUrl}owner/delete-offer/";
  static const updateOffer = "${apiBaseUrl}owner/update-offer/";
  static const replyComment = "${apiBaseUrl}owner/reply-comment/";
  static const myPlaces = "${apiBaseUrl}owner/my-place/";
  static const logoutOwner = "${apiBaseUrl}owner/logout-owner/";
  static const logoutUser = "${apiBaseUrl}user/logout-user/";

  // Dashboard and analytics endpoints
  static const dashboardOwner = "${apiBaseUrl}owner/dashboard-owner";
  static const updatePlaceOwner = "${apiBaseUrl}owner/update-place/";
  static const userRating = "${apiBaseUrl}user/rating/";
  static const vistePlace = "${apiBaseUrl}visite-place/";
  static const visite = "${apiBaseUrl}visite";
}
