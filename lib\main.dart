import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ma3ak/app/app.dart';

/// Global storage instance for app-wide data persistence
/// Used for storing user preferences, authentication tokens, and app state
final box = GetStorage();

/// Application entry point - initializes storage and launches the app
/// Sets up Flutter bindings and GetStorage before starting the main app widget
void main() async {
  // Ensure Flutter framework is initialized before async operations
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize GetStorage for local data persistence
  await GetStorage.init();

  // Launch the main application widget
  runApp(const MyApp());
}
