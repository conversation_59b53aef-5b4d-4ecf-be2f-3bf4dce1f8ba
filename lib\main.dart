import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ma3ak/app/app.dart';

// Global storage instance for app-wide data persistence
final box = GetStorage();

// Application entry point - initializes storage and launches the app
void main() async {
    WidgetsFlutterBinding.ensureInitialized();
    await GetStorage.init();
  runApp(const MyApp());
  }
