import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/offers/models/place_informaion_model.dart';
import 'package:ma3ak/app/modules/offers/models/place_information_old_model.dart';
import 'package:ma3ak/app/modules/place/models/comments_model.dart';
import 'package:ma3ak/app/modules/place/models/ctegoreis_model.dart';
import 'package:ma3ak/app/modules/place/models/place_model.dart';
import 'package:ma3ak/app/modules/place/models/sections_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';
import 'package:url_launcher/url_launcher_string.dart';

// Controller for managing places, sections, categories and search functionality
class PlaceController extends GetxController {
  // Loading states for UI feedback
  var isLoading = true.obs;
  var isLoadingInfo = true.obs;
  var showComments = false.obs;
  var commentsList = <Comment>[].obs;
  
  // Data models for places and related information
  Rx<PlaceModel> placeData = PlaceModel().obs;
  Rx<PlaceInfromationModel> placeInfromation = PlaceInfromationModel().obs;
  Rx<PlaceInfromationOldModel> placeInfromationOld =
      PlaceInfromationOldModel().obs;
  Rx<SectionsModel> sectionsData = SectionsModel().obs;
  // Rx<SectionsModel> resultSectionsData = SectionsModel().obs;
  Rx<CategoriesModel> resultSectionData = CategoriesModel().obs;
  Rx<CategoriesModel>? categoriesData = CategoriesModel().obs;
  
  // Search and filtering variables
  RxList<PlaceDatum>? resultPlaceList = <PlaceDatum>[].obs;
  TextEditingController searchPlaceController = TextEditingController();
  Rx<int?> selectedSection = Rx<int?>(null);
  Rx<int?> selectedCategory = Rx<int?>(null);
  RxList<Place> allPlaces = <Place>[].obs;
  RxList<Place>? resultAllPlaces = <Place>[].obs;

  // Opens WhatsApp with the provided phone number
  void openWhatsApp(String phoneNumber) async {
    final url = 'https://wa.me/+967$phoneNumber'; // URL scheme for WhatsApp
    if (await canLaunchUrlString(url)) {
      await launchUrlString(url,mode: LaunchMode.externalApplication);
    } else {
      throw 'Could not open WhatsApp';
    }
  }

  // Copies phone number to clipboard and shows confirmation
  void copyPhoneNumber(String phoneNumber) {
    // Copy the phone number to the clipboard
    Clipboard.setData(ClipboardData(text: phoneNumber)).then((_) {
      // Optionally, you can show a snackbar or a toast to notify the user
      Get.snackbar(
      backgroundColor: Colors.green,
      colorText: Colors.white
        ,"تم نسخ رقم الهاتف", "رقم الهاتف $phoneNumber", 
          snackPosition: SnackPosition.TOP);
    });
  }




  // Handles section selection change and fetches related categories
  void onSectionChanged(int? newValue) {
    // Set the selected section to the new value
    selectedSection.value = newValue;

    // Find the slug for the selected section
    final selectedSectionData = sectionsData.value.data?.firstWhere(
      (section) => section.id == newValue,
      // orElse: () => null, // Return null if no matching section is found
    );

    // If a section was found, call the function with the slug
    if (selectedSectionData != null) {
      // You can  now pass the `slug` to the function
      fetchCategoriesAndPlaceBySection(slugSecetion: selectedSectionData.slug!);
    } else {
      // Handle case where the section wasn't found (optional)
      debugPrint("Section not found!");
    }
  }

  // Handles category selection change for filtering places
  void onCategoryChanged(int? newCategory) {
    selectedCategory.value = newCategory;

    // If category is selected, filter places belonging to this category
    if (categoriesData?.value.data != null) {
      final selectedCategoryData = categoriesData?.value.data?.firstWhere(
        (category) => category.id == newCategory,
        // orElse: () => null,
      );

      if (selectedCategoryData != null) {
        // filterPlacesByCategory(selectedCategoryData);
      }
    }
  }

  // Fetches all places from API
  Future<void> fetchPlaces() async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .fetchPlaces(); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      placeData.value = dataModel;
      resultPlaceList?.value = dataModel.data!;
    });
  }

  // Gets detailed information for a specific place
  Future<Either<String, PlaceInfromationOldModel>> getPlaceInforamtion(
      {required slugPlace}) async {
    isLoadingInfo(true);
    final data = await RemoteServicesImpl().getPlaceInformation(
        slugPlace: slugPlace); // Make sure this method exists
    isLoadingInfo(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      placeInfromationOld.value = dataModel;
    });
    return data;
  }

  // Fetches all available sections from API
  Future<void> fetchSections() async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .fetchSections(); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      sectionsData.value = dataModel;
    });
  }

  // Fetches categories and places for a specific section
  Future<void> fetchCategoriesAndPlaceBySection(
      {required String slugSecetion}) async {
    isLoading(true);
    final data = await RemoteServicesImpl().fetchCategoriesAndPlaceBySection(
        slugScetion: slugSecetion); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      categoriesData?.value = dataModel;
      final categoriesCards = categoriesData?.value.data;
      if (categoriesCards != null) {
        for (var category in categoriesCards) {
          allPlaces.addAll(category.places);
        }
      }
    });
  }

  // Filters places based on search text input
  void searchPlaces(String text) {
    // If categories exist, gather all places from them

    if (text.isEmpty) {
      resultPlaceList?.value = placeData.value.data!;
      resultAllPlaces?.value = allPlaces;
      // resultSectionData.value = categoriesData.value.data;
    } else {
      resultPlaceList?.value = placeData.value.data!
          .where(
            (element) =>
                element.name.toLowerCase().contains(text.toLowerCase()),
          )
          .toList();
      resultAllPlaces?.value = allPlaces
          .where(
            (element) =>
                element.name.toLowerCase().contains(text.toLowerCase()),
          )
          .toList();
    }
  }

  // Initializes controller by fetching initial data
  @override
  void onInit() async {
    await fetchPlaces();
    await fetchSections();
    // resultPlaceList?.value = placeData.value.data!;
    // fetchComments(); // Fetch comments when the controller is initialized
    super.onInit();
  }

  // Toggles the visibility of comments section
  void toggleComments() {
    showComments.value = !showComments.value;
  }

  // void fetchComments() async {
  //   isLoading(true);
  //   final result = await RemoteServicesImpl().fetchComments();
  //   result.fold((error) {
  //     /// This method is marked as `async` because it waits for the API to respond.
  //     /// It also sets the [isLoading] observable to `true` while it is waiting for the API response,
  //     /// and then sets it back to `false` after the response is received.
  //     // Handle error
  //     // print("Error: $error");
  //   }, (commentsModel) {
  //     // Assign the comments to the observable list
  //     commentsList.assignAll(commentsModel.data!.first.comment);
  //   });
  //   isLoading(false);
  // }
}
