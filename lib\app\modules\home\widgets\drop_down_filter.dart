import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DropDownFilter extends StatelessWidget {
  const DropDownFilter({
    super.key,
    this.onChanged,
    this.items,
    this.value,
    this.hint = 'Filter',
  });
  final void Function(int?)? onChanged;
  final List<DropdownMenuItem<int>>? items;
  final int? value;
  final String? hint;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 5),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        height: 40.h,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey), // Border color
          borderRadius: BorderRadius.circular(8.r), // Circular border
        ),
        child: DropdownButton(
          hint: Text(hint!),
          icon: const Icon(Icons.expand_more),
          padding: EdgeInsets.zero,
          underline: const SizedBox(),
          value: value,
          items: items,
          onChanged: onChanged,
        ),
      ),
    );
  }
}
