import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ma3ak/app/modules/offers/models/place_information_old_model.dart';

class CommentsPlaceTestPassAll extends StatelessWidget {
  const CommentsPlaceTestPassAll({super.key, this.placeComments});
  final List<Comments>? placeComments;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.only(bottom: 50.h),
      shrinkWrap: true,
      itemBuilder: (context, index) {
        var comment = placeComments![index];
        // var comment = placeDetails!.comments[index];

        return Column(
          children: [
            ListTile(
              contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
              leading: CircleAvatar(
                radius: 20.r,
              ),
              title: Text(comment.user!.name!),
              subtitle: Text(comment.comment!),
            ),
            comment.replies?.comment != null
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: RichText(
                          text: TextSpan(
                            text: 'الرد على ', // Default text
                            style: const TextStyle(
                                color: Colors
                                    .black), // Default color for Arabic text
                            children: <TextSpan>[
                              TextSpan(
                                text: '${comment.user!.name!} ', // Name in blue
                                style: const TextStyle(
                                    color:
                                        Colors.blue), // Blue color for the name
                              ),
                              const TextSpan(
                                text: ':',
                                style: TextStyle(
                                    color:
                                        Colors.black), //// Colon after the name
                              ),
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Row(
                          children: [
                            // Adding some space between avatar and text
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    comment.replies?.user?.name ?? '',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Text(comment.replies?.comment ?? ''),
                                ],
                              ),
                            ),
                            SizedBox(width: 8.w),
                            CircleAvatar(
                              radius: 16.r,
                            ),
                          ],
                        ),
                      ),
                    ],
                  )
                : const SizedBox(),
          ],
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 10.h,
        );
      },
      itemCount: placeComments?.length ?? 0,
    );
  }
}
