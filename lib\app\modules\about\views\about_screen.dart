import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/utils/images_pic.dart';
import 'package:ma3ak/widgets/widgets/basic_app_bar.dart';

/// About screen displaying app information and contact details
/// Shows app logo, description, and social media contact buttons
class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Column(
      children: [
        // App bar with primary color background
        const BasicAppbar(backgroundColor: AppColor.primaryColor),
        // Top section with logo and app name
        Expanded(
          flex: 1,
          child: Container(
            width: double.infinity,
            color: AppColor.primaryColor, 
            child: Column(
              children: [
                // App logo with responsive width
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.4,
                  child: Image.asset(ImagesPic.logoM),
                ),
                const SizedBox(
                  height: 10,
                ),
                // App name in Arabic
                Flexible(
                  child: Text(
                    'تطبيق معاك', // "Ma3ak App"
                    style: TextStyle(
                      color: AppColor.whiteColor,
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
        // Bottom section with description and contact info
        Expanded(
          flex: 2,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Description section title
                  Text(
                    'الوصف', // "Description"
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  // App description text in Arabic
                  Text(
                    'هو تطبيق يهدف الى تسهيل البحث  والوصول الى اي مكان سواء كان مستشفيات او جامعات او مراكز حكومية او خاصة او اعمال حرة, وعرض اعلانات او وظائف او دورات عن تلك الأماكن.',
                    style: TextStyle(
                      fontSize: 16.sp,
                    ),
                    textAlign: TextAlign.justify,
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  // Contact section title
                  Text(
                    'للتواصل معنا', // "Contact Us"
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  // Row of social media contact buttons
                  const Row(
                    children: [
                      IconButtonWidget(),
                      IconButtonWidget(),
                      IconButtonWidget(),
                      IconButtonWidget(),
                    ],
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    ));
  }
}

/// Reusable icon button widget for social media contacts
/// Currently displays Facebook icon (placeholder for actual social links)
class IconButtonWidget extends StatelessWidget {
  const IconButtonWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // Social media icon button (currently Facebook placeholder)
    return IconButton(onPressed: () {}, icon: const Icon(Icons.facebook));
  }
}
