import 'package:flutter/material.dart';
import 'package:ma3ak/config/theme/app_color.dart';

/// Customizable basic app bar with optional title, action, and back button
/// Implements PreferredSizeWidget for use as AppBar replacement
class BasicAppbar extends StatelessWidget implements PreferredSizeWidget {
  // App bar configuration properties
  final Widget? title; // Custom title widget
  final Widget? action; // Action widget (usually button or icon)
  final Color? backgroundColor; // Background color override
  final bool hideBack; // Flag to hide back button
  final double? height; // Custom height override
  
  const BasicAppbar(
      {this.title,
      this.hideBack = false,
      this.action,
      this.backgroundColor,
      this.height,
      super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: backgroundColor ?? Colors.transparent,
      elevation: 0,
      centerTitle: true,
      automaticallyImplyLeading: false,
      toolbarHeight: height ?? 80, // Default height of 80
      title: title ?? const Text(''),
      titleSpacing: 0,
      actions: [action ?? Container()],
      // Conditional back button with custom circular design
      leading: hideBack
          ? null
          : IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              // Custom circular back button with black background
              icon: Container(
                height: 50,
                width: 50,
                decoration: const BoxDecoration(
                    color: AppColor.blackColor, //AppColors.secondBackground,
                    shape: BoxShape.circle),
                child: const Icon(Icons.arrow_back_ios_new,
                    size: 15, color: Colors.white),
              ),
            ),
    );
  }

  // Required for PreferredSizeWidget implementation
  @override
  Size get preferredSize => Size.fromHeight(height ?? 80);
}
