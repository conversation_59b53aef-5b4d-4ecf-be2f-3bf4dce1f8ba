import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/comments_place_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class CommentsController extends GetxController {
  var isLoading = false.obs;
  var hasFetchedComments = false.obs;
  Rx<CommentsPlaceModel>? commentsData = CommentsPlaceModel().obs;

  Future<void> fetchCommentsPlace(String slugPlace) async {
    // if (hasFetchedComments.value) return;
    isLoading(true);
    final result =
        await RemoteServicesImpl().fetchCommentsPlace(slugPlace: slugPlace);
    // hasFetchedComments.value = true;
    result.fold((error) {
      debugPrint(error.toString());
    }, (commentsModel) {
      commentsData?.value = commentsModel;
      update();
    });
    isLoading(false);
  }
}
