import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/comments_place_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for managing place comments data and loading states
class CommentsController extends GetxController {
  // Loading state indicator for API requests
  var isLoading = false.obs;

  // Flag to track if comments have been fetched (currently unused)
  var hasFetchedComments = false.obs;

  // Observable container for comments data from API
  Rx<CommentsPlaceModel>? commentsData = CommentsPlaceModel().obs;

  /// Fetches comments for a specific place using its slug identifier
  Future<void> fetchCommentsPlace(String slugPlace) async {
    // if (hasFetchedComments.value) return;
    isLoading(true);

    // Call API service to retrieve place comments
    final result =
        await RemoteServicesImpl().fetchCommentsPlace(slugPlace: slugPlace);
    // hasFetchedComments.value = true;

    // Handle API response using Either pattern
    result.fold((error) {
      debugPrint(error.toString());
    }, (commentsModel) {
      commentsData?.value = commentsModel;
      update(); // Notify UI of data changes
    });
    isLoading(false);
  }
}
