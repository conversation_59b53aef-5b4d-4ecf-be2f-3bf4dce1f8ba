import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TextRichWidget extends StatelessWidget {
  const TextRichWidget({
    super.key,
    required this.title,
    required this.titleValue,
  });
  final String title;
  final String titleValue;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Row(
        children: [
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: title,
                  style: TextStyle(
                    fontSize: 16.sp,
                  ),
                ),
                TextSpan(
                  text: titleValue,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18.sp,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
