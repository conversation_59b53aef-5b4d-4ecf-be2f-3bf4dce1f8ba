import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/controller/auth_controller.dart';
import 'package:ma3ak/app/modules/favorite/controller/favorite_controller.dart';
import 'package:ma3ak/app/modules/home/<USER>/ads_controller.dart';
import 'package:ma3ak/app/modules/my_offers/controller/send_reply_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/send_commen_controller.dart';
import 'package:ma3ak/app/modules/place/controller/comments_controller.dart';
import 'package:ma3ak/app/modules/place/controller/greater_rate_place_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/search/controller/search_controllers.dart';
import 'package:ma3ak/data/api/api_server.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

import '../controller/navbar_controller.dart';

// Dependency injection bindings for navbar and related controllers
class NavbarBinding extends Bindings {
  // Registers all controllers and services needed for main navigation
  @override
  void dependencies() {
    // Core services and API layer
    Get.lazyPut(() => ApiServer());
    Get.lazyPut(() => RemoteServicesImpl());
    
    // Navigation and authentication controllers
    Get.lazyPut(() => NavbarController());
    Get.lazyPut(() => AuthController());
    
    // Feature controllers
    Get.lazyPut(() => AdsController());
    Get.lazyPut(() => PlaceController());
    Get.lazyPut(() => GreaterRatePlaceController());
    Get.lazyPut(() => OffersController());
    Get.lazyPut(() => FavoriteController());
    Get.lazyPut(() => CommentsController());
    Get.lazyPut(() => SendCommentController());
    Get.lazyPut(() => SendReplyController());
    Get.lazyPut(() => SearchPlaceController());

    // Get.put(ApiServer());
    // Get.put(RemoteServicesImpl());

    // Get.put(NavbarController());
    // Get.put(AuthController());
  }
}
