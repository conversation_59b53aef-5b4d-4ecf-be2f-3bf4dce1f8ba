import 'dart:convert';

// To parse this JSON data, do
//
//     final ownerProfileModel = ownerProfileModelFromJson(jsonString);

OwnerProfileModel ownerProfileModelFromJson(String str) =>
    OwnerProfileModel.fromJson(json.decode(str));

String ownerProfileModelToJson(OwnerProfileModel data) =>
    json.encode(data.toJson());

class OwnerProfileModel {
  final bool? status;  // Changed to nullable
  final String? message; // Changed to nullable
  final Owner? owner; // Owner object can be null

  OwnerProfileModel({
    this.status,
    this.message,
    this.owner,
  });

  factory OwnerProfileModel.fromJson(Map<String, dynamic> json) =>
      OwnerProfileModel(
        status: json["status"],
        message: json["message"],
        owner: json["date"] != null ? Owner.fromJson(json["date"]) : null, // Using "date" for owner data
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "date": owner?.toJson(), // Convert owner to JSON if it's not null
      };
}

class Owner {
  final int? id; // Changed to nullable
  final String? name; // Changed to nullable
  final String? email; // Changed to nullable
  final String? phoneNumber; // Changed to nullable
  final int? isOwner; // Changed to nullable
  final String? slug; // Changed to nullable
  final String? img; // Changed to nullable
  final String? emailVerifiedAt; // Remains nullable
  final String? status; // Changed to nullable
  final DateTime? createdAt; // Changed to nullable
  final DateTime? updatedAt; // Changed to nullable

  Owner({
    this.id,
    this.name,
    this.email,
    this.phoneNumber,
    this.isOwner,
    this.slug,
    this.img,
    this.emailVerifiedAt,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory Owner.fromJson(Map<String, dynamic> json) => Owner(
        id: json["id"],
        name: json["name"],
        email: json["email"],
        phoneNumber: json["phone_number"],
        isOwner: json["isOwner"],
        slug: json["slug"],
        img: json["img"],
        emailVerifiedAt: json["email_verified_at"],
        status: json["status"],
        createdAt: json["created_at"] != null ? DateTime.parse(json["created_at"]) : null,
        updatedAt: json["updated_at"] != null ? DateTime.parse(json["updated_at"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "email": email,
        "phone_number": phoneNumber,
        "isOwner": isOwner,
        "slug": slug,
        "img": img,
        "email_verified_at": emailVerifiedAt,
        "status": status,
        "created_at": createdAt?.toIso8601String(), // Convert to string if not null
        "updated_at": updatedAt?.toIso8601String(), // Convert to string if not null
      };
}
