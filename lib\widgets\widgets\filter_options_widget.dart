import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/search/controller/search_controllers.dart';
import 'package:ma3ak/app/modules/search/widgets/auto_complete_sections_widget.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/widgets/shimmer/container_drop_shimmer.dart';

/// Filter options widget for search functionality
/// Contains filter icon and autocomplete section selector with toggle button
class FilterOptionsWidget extends StatelessWidget {
  FilterOptionsWidget({
    super.key,
  });
  
  // Search controller for managing filter state
  final SearchPlaceController searchPlaceController =
      Get.find<SearchPlaceController>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Filter icon container with gray background
          Container(
            height: 40.h,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
                color: AppColor.grayColor,
                borderRadius: BorderRadius.only(topLeft: Radius.circular(4.r))),
            child: const Icon(
              Icons.filter_alt_outlined,
            ),
          ),
          const SizedBox(
            width: 5,
          ),
          // Expandable autocomplete section selector
          Expanded(
            child: Obx(() {
              // Show shimmer while data is loading
              if (searchPlaceController.sectionsData.value.data == null) {
                return const ContainerDropShimmer();
              }
              // Show autocomplete widget with toggle button
              return AutocompleteSearchBySectionWidget(
                suffixIcon: IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    // Toggle clear sections state
                    searchPlaceController.isClearSections.value =
                        !searchPlaceController.isClearSections.value;
                  },
                  // Dynamic icon based on clear state (check/close)
                  icon: Obx(
                    () => searchPlaceController.isClearSections.value == true
                        ? const Icon(
                            color: Colors.green,
                            Icons.check,
                          )
                        : const Icon(
                            color: Colors.red,
                            Icons.close,
                          ),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(
            width: 10,
          ),
          // Placeholder space for future filter option
          Expanded(
              child: SizedBox(
            height: 40.h,
            width: 70.w,
          )
              // TODO: Commented out second autocomplete widget
              //  Obx(() {
              //   if (searchPlaceController.sectionsData.value.data == null) {
              //     return const ContainerDropShimmer();
              //   }
              //   return AutocompleteSearchBySectionWidget();
              // }),
              ),
        ],
      ),
    );
  }
}
