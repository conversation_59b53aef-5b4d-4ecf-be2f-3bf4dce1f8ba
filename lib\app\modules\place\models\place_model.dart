import 'dart:convert';

// To parse this JSON data, do
//
//     final placeModel = placeModelFromJson(jsonString);

PlaceModel placeModelFromJson(String str) => PlaceModel.fromJson(json.decode(str));

String placeModelToJson(PlaceModel data) => json.encode(data.toJson());

class PlaceModel {
  final bool? status;
  final String? message;
  final List<PlaceDatum>? data;

    PlaceModel({
     this.status,
     this.message,
     this.data,
  });

  factory PlaceModel.fromJson(Map<String, dynamic> json) => PlaceModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<PlaceDatum>.from(json["data"].map((x) => PlaceDatum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class PlaceDatum {
  final int id;
  final String name;
  final String governor;
  final String directorate;
  final String addressDetails;
  final String des;
  final int whatsappNumber;
  final String coverImage;
  final String slug;
  final int stars;
  final String status;
  final int? parentId;
  final int catId;
  final int ownerId;

  PlaceDatum({
    required this.id,
    required this.name,
    required this.governor,
    required this.directorate,
    required this.addressDetails,
    required this.des,
    required this.whatsappNumber,
    required this.coverImage,
    required this.slug,
    required this.stars,
    required this.status,
    this.parentId,
    required this.catId,
    required this.ownerId,
  });

  factory PlaceDatum.fromJson(Map<String, dynamic> json) => PlaceDatum(
        id: json["id"],
        name: json["name"],
        governor: json["governor"],
        directorate: json["directorate"],
        addressDetails: json["address_details"],
        des: json["des"],
        whatsappNumber: json["whatsapp_number"],
        coverImage: json["cover_image"],
        slug: json["slug"],
        stars: json["stars"],
        status: json["status"],
        parentId: json["parent_id"],
        catId: json["cat_id"],
        ownerId: json["owner_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "governor": governor,
        "directorate": directorate,
        "address_details": addressDetails,
        "des": des,
        "whatsapp_number": whatsappNumber,
        "cover_image": coverImage,
        "slug": slug,
        "stars": stars,
        "status": status,
        "parent_id": parentId,
        "cat_id": catId,
        "owner_id": ownerId,
      };
}
