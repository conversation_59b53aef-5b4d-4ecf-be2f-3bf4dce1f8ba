/// Model representing API response for rating submission
class RatingModel {
  // Response status indicating success/failure
  bool? status;

  // Response message with additional information
  String? message;

  RatingModel({this.status, this.message});

  // Creates RatingModel from JSON response
  RatingModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
  }

  // Converts RatingModel to JSON format
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    return data;
  }
}
